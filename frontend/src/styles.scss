/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;
@import './assets/themes/bluetheme.css';
@import './assets/themes/redtheme.css';
@import './assets/themes/yellowtheme.css';
@import './assets/themes/purpletheme.css';
@import './assets/themes/greentheme.css';
@import './assets/themes/orangetheme.css';

// /* Regular - 400 */
@font-face {
  font-family: 'Montserrat';
  src: url('./assets/Fonts/Montserrat/Montserrat-Regular.ttf')
    format('truetype');
  font-weight: 400;
  font-style: normal;
}

/* Medium - 500 */
@font-face {
  font-family: 'MontserratMedium';
  src: url('./assets/Fonts/Montserrat/Montserrat-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

// /* SemiBold - 600 */
@font-face {
  font-family: 'MontserratSemiBold';
  src: url('./assets/Fonts/Montserrat/Montserrat-SemiBold.ttf')
    format('truetype');
  font-weight: 600;
  font-style: normal;
}

/* Global font use */
* {
  font-family: 'Montserrat';
}

html {
  color-scheme: light;
  --primary-bg: #fff;
  --gray-bg: #f2f2f2;
  --chips-bg: #dcdcdc;
}

html.dark-mode {
  color-scheme: dark;
  --primary-bg: #001d33;
  --gray-bg: #222121;
  --chips-bg: #2c3741;
}

body.red-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.green-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.blue-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.yellow-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.orange-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.purple-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

@include mat.chips-overrides(
  (
    container-height: 40px,
    container-shape-radius: '40px',
  )
);

@include mat.form-field-overrides(
  (
    container-height: '30px',
    filled-active-indicator-height: '2px',
  )
);

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}
body {
  margin: 0;
  font-family: 'Montserrat', 'Helvetica Neue', sans-serif;
}
.mat-mdc-snack-bar-container.success-snk {
  --mdc-snackbar-container-color: white;
  --mdc-snackbar-supporting-text-color: rgba(0, 0, 0, 0.87);
  --mat-snack-bar-button-color: rgba(0, 0, 0, 0.6);
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  --mdc-snackbar-container-shape: 12px;
  border: 1px solid #28a745;
  border-left: 6px solid #28a745;
  border-radius: 12px;
  box-sizing: border-box;
  overflow: visible;
}
.success-snk .mdc-snackbar__surface {
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}
.mat-mdc-snack-bar-container.failed-snk {
  --mdc-snackbar-container-color: white;
  --mdc-snackbar-supporting-text-color: rgba(0, 0, 0, 0.87);
  --mat-snack-bar-button-color: rgba(0, 0, 0, 0.6);
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  --mdc-snackbar-container-shape: 12px;
  border: 1px solid red;
  border-left: 6px solid red;
  border-radius: 12px;
  box-sizing: border-box;
  overflow: visible;
}
.failed-snk .mdc-snackbar__surface {
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}
.dark-mode .mat-mdc-snack-bar-container.success-snk {
  --mdc-snackbar-container-color: #1e1e1e;
  --mdc-snackbar-supporting-text-color: #e0ffe0;
  --mat-snack-bar-button-color: #88ff88;
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  border: 1px solid #28a745;
  border-left: 6px solid #28a745;
  border-radius: 12px;
}

.dark-mode .mat-mdc-snack-bar-container.failed-snk {
  --mdc-snackbar-container-color: #1e1e1e;
  --mdc-snackbar-supporting-text-color: #ffd6d6;
  --mat-snack-bar-button-color: #ff8888;
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  border: 1px solid red;
  border-left: 6px solid red;
  border-radius: 12px;
}

/* Hide the default password reveal icon in modern browsers */
input[type='password']::-webkit-password-revealer {
  display: none;
  -webkit-appearance: none;
}

/* Hide the default password reveal icon in older Edge/IE */
input[type='password']::-ms-reveal {
  display: none;
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url('./assets/Icons/materialIcon.woff2') format('woff2');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
// styles.scss
.custom-toggle.mat-slide-toggle {
  display: inline-block;
  min-height: 20px;
  text-align: left;
  cursor: pointer;
}

.custom-toggle .mat-slide-toggle-thumb {
  background-color: white;
}

.custom-toggle.mat-checked .mat-slide-toggle-bar {
  background-color: #4097db;
}

//Alert Dialog Styles
.alert-dialog .mat-mdc-dialog-surface {
  border-radius: 10px;
}

.alert-dialog .mat-mdc-dialog-actions {
  padding: 20px;
}
::ng-deep .mat-dialog-container {
  border: 2px solid #2196f3;
  border-radius: 8px;
}

.custom-dialog-container {
  border-radius: 0 !important;
}
:root {
  @include mat.dialog-overrides(
    (
      container-shape: rectangle,
      container-color: var(--mat-background),
    )
  );
}

.no-round.mdc-button {
  border-radius: 5px !important;
}
.small-toggle {
  transform: scale(0.75); /* scales down to 75% size */
  transform-origin: left center; /* keep toggle aligned properly */
  margin-left: 8px; /* adjust margin if needed */
}
.small-toggle .mdc-switch__icons {
  background-color: #ffffff;
  border-radius: 30px;
}

.mat-mdc-chip {
  background-color: var(--chips-bg) !important;
}
.mdc-text-field--outlined .mat-mdc-form-field-infix,
.mdc-text-field--no-label .mat-mdc-form-field-infix {
  padding-top: var(
    --mat-form-field-container-vertical-padding,
    10px
  ) !important;
  padding-bottom: var(
    --mat-form-field-container-vertical-padding,
    10px
  ) !important;
}
.dashboard-content .mat-mdc-tab.mdc-tab {
  flex-grow: 0 !important;
}

.mat-sort-header-arrow {
  color: var(--mat-sys-surface) !important;
}
.mat-mdc-dialog-content {
  max-height: 75vh !important;
}
.disabled-input .mat-mdc-text-field-wrapper {
  background-color: var(--gray-bg);
  pointer-events: none;
}
