import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './core/guard/auth.guard';
import { ResetPasswordGuard } from './core/guard/reset-password.guard';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./authentication/authentication.module').then(
        (m) => m.AuthenticationModule
      ),
  },
  {
    path: 'app',
    loadChildren: () =>
      import('./main-app/main-app.module').then((m) => m.MainAppModule),
    canActivateChild: [AuthGuard, ResetPasswordGuard],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
