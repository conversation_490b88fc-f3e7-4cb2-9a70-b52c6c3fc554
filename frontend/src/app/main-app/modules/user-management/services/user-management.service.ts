import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { delay, map, Observable, of, switchMap } from 'rxjs';
import { departmenturl, userUrl } from '../../../../app.constant';
import { ShiftManagementService } from '../../shift-management/services/shift-management.service';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  constructor(
    private http: HttpClient,
    private shiftMgmtService: ShiftManagementService
  ) {}

  getDepartmentsAndShifts() {
    return this.http.get(departmenturl + 'list').pipe(
      switchMap((departments) => {
        return this.shiftMgmtService.getShifts().pipe(
          map((shifts) => {
            return { departments, shifts };
          })
        );
      })
    );
  }

  addUser(user: any): Observable<any> {
    return this.http.post(userUrl + 'create', user);
  }

  updateUser(user: any, userId: string): Observable<any> {
    return this.http.put(userUrl + 'update?id=' + userId, user);
  }

  deleteUser(userId: string): Observable<any> {
    return this.http.delete(userUrl + 'delete/' + userId);
  }

  getUsers(
    pageIndex: number,
    pageSize: number,
    sortBy: string,
    sortDirection: 'asc' | 'desc' | '',
    searchQuery: string
  ): Observable<any> {
    const params: any = {
      page: pageIndex + 1, // API usually expects 1-based page index
      limit: pageSize,
      sortBy: sortBy,
      sortDirection: sortDirection,
      search: searchQuery,
    };

    // Example: append query parameters to the URL
    const queryString = Object.keys(params)
      .filter(
        (key) =>
          params[key] !== undefined &&
          params[key] !== null &&
          params[key] !== ''
      )
      .map(
        (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
      )
      .join('&');

    const fullUrl = `${userUrl}list?${queryString}`;
    return this.http.get<any>(fullUrl);
  }
}
