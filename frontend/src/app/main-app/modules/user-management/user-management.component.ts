import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IUser, IUserDepartment, IUserDialogResult } from './modal/user.modal';
import { MatDialog } from '@angular/material/dialog';
import { AddEditUserComponent } from './add-edit-user/add-edit-user.component';
import { merge, of as observableOf, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  switchMap,
  takeUntil,
} from 'rxjs/operators';
import { UserManagementService } from './services/user-management.service';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { USER_ROLES } from '../../../app.constant';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'cnc-user-management',
  standalone: false,
  templateUrl: './user-management.component.html',
  styleUrl: './user-management.component.scss',
})
export class UserManagementComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  loader = false;
  displayedColumns: string[] = [
    'firstname',
    'lastname',
    'phone',
    'email',
    'username',
    'role',
    'departments',
    'shift',
  ];
  dataSource: MatTableDataSource<IUser> = new MatTableDataSource<IUser>([]);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  currentUserRole = inject(AuthService).currentUser?.role;
  selectedRow: IUser | null = null;
  departmentList: IUserDepartment[] = [];
  shiftList: { id: string; name: string }[] = [];
  originalData: IUser[] = [];
  private destroy$ = new Subject<void>(); // For unsubscribing observables
  constructor(
    private dialog: MatDialog,
    private authService: AuthService,
    private userService: UserManagementService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.userService.getDepartmentsAndShifts().subscribe({
      next: (res: any) => {
        this.departmentList = res.departments.data.map((dept: any) => ({
          id: dept.id,
          name: dept.name,
        }));
        this.shiftList = res.shifts.data.map((shift: any) => ({
          name: shift.shift_name,
          id: shift.id,
          departments: shift.departments,
        }));
      },
      error: (error: any) =>
        console.error('Error fetching departments and shifts:', error),
    });
  }

  getUsers() {
    this.userService
      .getUsers(0, 200, this.sort.active, this.sort.direction, '')
      .subscribe({
        next: (res: any) => {
          this.loader = false;
          const { data } = res || {};
          if (!data) {
            this.dataSource.data = [];
            this.originalData = [];
            return;
          }

          const [{ users, total }] = data || [];
          this.dataSource.data = users;
          this.originalData = users;
          this.paginator.length = total;
        },
      });
  }

  getDepartmentName(departments: string[]): string {
    return (
      this.departmentList.find((dp: IUserDepartment) =>
        departments?.includes(dp.id)
      )?.name || ''
    );
  }

  getShiftName(shifts: string[]): string {
    return this.shiftList.find((s) => shifts?.includes(s.id))?.name || '';
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.getUsers();
    this.sort.sortChange
      .pipe(takeUntil(this.destroy$))
      .subscribe((response) => {
        this.dataSource.data = arraySortByKey(
          this.originalData,
          response.active,
          response.direction === 'asc' ? 'asc' : 'desc'
        );
      });
  }

  onSearch(args: any) {
    const value = args.value.trim().toLowerCase();
    this.dataSource.data = this.originalData.filter((user: IUser) => {
      return (
        user.firstname.toLowerCase().includes(value) ||
        user.lastname.toLowerCase().includes(value) ||
        user.email.toLowerCase().includes(value) ||
        user.username.toLowerCase().includes(value) ||
        user.phone.toLowerCase().includes(value) ||
        user.role.toLowerCase().includes(value) ||
        this.getDepartmentName(user.departments)
          .toLowerCase()
          .includes(value) ||
        this.getShiftName(user.shifts).toLowerCase().includes(value)
      );
    });
  }

  onAddUser() {
    const dialogRef = this.dialog.open(AddEditUserComponent, {
      data: {
        departmentList: this.departmentList,
        shiftList: this.shiftList,
        title: 'Add Manager',
        isEdit: false,
        source: {
          id: 0,
          firstName: '',
          lastName: '',
          phone: '',
          email: '',
          username: '',
          role:
            this.authService.currentUser?.role === USER_ROLES.ADMIN
              ? USER_ROLES.USERROLE1
              : USER_ROLES.ADMIN,
          departments: [],
          shift: [],
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((result: IUserDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.addUser(data);
      }
    });
  }

  addUser(data: IUser): void {
    this.loader = true;
    this.userService.addUser(data).subscribe({
      next: () => {
        this.getUsers();
        this.onSuccess('User added successfully!');
      },
      error: (err: any) => {
        this.reOpenDialog(data, false);
        this.onError('Failed to add user', err.error_message);
        this.loader = false;
        // this.onError('Failed to add user', err.message);
        if (err.status === 409) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
        if (err.status === 400) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
      },
    });
  }

  reOpenDialog(dataobj: any, isEdit = false) {
    const dialogRef = this.dialog.open(AddEditUserComponent, {
      data: {
        departmentList: this.departmentList,
        shiftList: this.shiftList,
        title: 'Add Manager',
        isEdit: isEdit,
        source: {
          id: dataobj.id,
          firstName: dataobj.firstname,
          lastName: dataobj.lastname,
          phoneNo: dataobj.phone,
          email: dataobj.email,
          userName: dataobj.username,
          password: dataobj.password,
          role: dataobj.role,
          departments: dataobj.departments,
          shift: dataobj.shifts[0],
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((result: IUserDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.addUser(data);
      }
      if (action === 'update') {
        const { email, password, role, username, ...body } = data;
        this.updateUser(body, dataobj.id);
      } else if (action === 'delete') {
        this.deleteUser(dataobj.id);
      }
    });
  }

  onRowDoubleClick(args: any) {
    this.onRowSelect(args);
    const dialogRef = this.dialog.open(AddEditUserComponent, {
      data: {
        departmentList: this.departmentList,
        shiftList: this.shiftList,
        title: 'Edit Manager',
        isEdit: true,
        source: {
          firstName: args.firstname,
          lastName: args.lastname,
          phoneNo: args.phone,
          email: args.email,
          userName: args.username,
          password: 'Admin@123',
          shift: args.shifts ? args.shifts[0] : '',
          departments: args.departments,
          role: args.role,
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((result: IUserDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'update') {
        const { email, password, role, username, ...body } = data;
        this.updateUser(body, args.id);
      } else if (action === 'delete') {
        this.deleteUser(args.id);
      }
    });
  }

  updateUser(data: Partial<IUser>, userId: string): void {
    this.loader = true;
    this.userService.updateUser(data, userId).subscribe({
      next: (res: any) => {
        this.getUsers();
        this.onSuccess('User updated successfully!');
      },
      error: (err: any) => {
        if (err.status === 409) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
        if (err.status === 400) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
      },
    });
  }

  deleteUser(userId: string): void {
    this.loader = true;
    this.userService.deleteUser(userId).subscribe({
      next: (res: any) => {
        this.getUsers();
        this.onSuccess('User deleted successfully!');
      },
      error: (err: any) => {
        this.onError('Failed to delete user', err.message);
      },
    });
  }

  onRowSelect(args: any) {
    this.selectedRow = args;
  }

  onSuccess(message: string) {
    this.snackbarService.open(message, '', 'success', 3000);
  }

  onError(message: string, error: any) {
    this.loader = false;
    this.snackbarService.open(message, '', 'failed', 3000);
    console.error(message, error);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
function arraySortByKey(
  array: any[],
  key: string,
  order: 'asc' | 'desc' = 'asc'
): any[] {
  return array.sort((a, b) => {
    if (a[key] < b[key]) {
      return order === 'asc' ? -1 : 1;
    }
    if (a[key] > b[key]) {
      return order === 'asc' ? 1 : -1;
    }
    return 0;
  });
}
