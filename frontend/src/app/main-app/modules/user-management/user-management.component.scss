.container {
  height: 100%;
  display: grid;
  grid-template-rows: 50px 1fr;
}
.title {
  padding: 15px 0px;
  font-size: 20px;
  height: 30px;
  padding-bottom: 5px;
}
.title label {
  font-family: MontserratSemiBold;
}
.title label .badge {
  background-color: var(--mat-sys-primary);
  font-size: 18px;
  color: white;
  padding: 3px 10px;
  border-radius: 5px;
  margin-left: 10px;
}

.content-area {
  display: grid;
  grid-template-rows: 34px 1fr 50px;
}

.action-block {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
  margin-top: -25px;
}

.table-container {
  min-height: 400px;
  position: relative;
  max-height: 60vh;
  overflow: auto;
  position: relative;
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: -30px;
}

.actions-container button {
  margin-top: -25px;
}
::ng-deep .user-list-table thead th {
  background-color: var(--mat-sys-primary) !important;
  color: var(--mat-sys-inverse-on-surface);
  position: sticky;
  top: 0;
  z-index: 2;
}
::ng-deep .user-list-table thead th:first-child {
  border-top-left-radius: 10px;
}
::ng-deep .user-list-table thead th:last-child {
  border-top-right-radius: 10px;
}

::ng-deep .user-list-table tr {
  cursor: pointer;
}

table {
  border-color: var(
    --mat-table-row-item-outline-color,
    var(--mat-sys-outline, rgba(0, 0, 0, 0.12))
  );
  border-style: solid;
  border-width: 1px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.more-link {
  color: var(--mat-sys-primary);
  text-decoration: none;
  font-weight: 600;
  margin-left: 8px;
  &:hover {
    text-decoration: underline;
  }
}

mat-paginator {
  display: flex;
}

.selected-row {
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
}

.dropmenu-dept li {
  list-style: none;
  padding: 10px 20px;
  border-bottom: 1px solid var(--mat-sys-outline);
}
.dept-name {
  float: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
