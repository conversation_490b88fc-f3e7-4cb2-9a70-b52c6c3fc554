import { Component, inject, Inject } from '@angular/core';
import {
  <PERSON><PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { IUser, IUserDepartment } from '../modal/user.modal';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {
  STRONGPASSWORDPATTERN,
  USER_ROLES,
  USER_ROLES_LIST,
} from '../../../../app.constant';
import { AuthService } from '../../../../core/services/auth.service';
import { IShift } from '../../shift-management/modal/shift-mgmt.modal';

@Component({
  selector: 'cnc-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.scss',
})
export class AddEditUserComponent {
  userForm: FormGroup;
  allDepartments: IUserDepartment[] = [];
  shiftList: { id: string; name: string }[] = [];
  shifts: any = [];
  hidePassword = true;
  currentUserRole = inject(AuthService).currentUser?.role;
  userRoles = Object.entries(USER_ROLES_LIST).filter(
    ([key, v]) => key !== USER_ROLES.SUPERADMIN && key !== USER_ROLES.ADMIN
  );
  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<AddEditUserComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.userForm = this.fb.group({
      firstName: [data.source.firstName, Validators.required],
      lastName: [data.source.lastName, Validators.required],
      phoneNo: [
        data.source.phoneNo,
        [Validators.required, Validators.pattern(/^\d{10,}$/)],
      ],
      email: [
        { value: data.source.email, disabled: data.isEdit },
        [
          Validators.required,
          Validators.email,
          Validators.pattern(
            /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
          ),
        ],
      ],
      userName: [
        { value: data.source.userName, disabled: data.isEdit },
        [
          Validators.required,
          Validators.minLength(3),
          Validators.pattern(/^[a-zA-Z0-9]+$/),
        ],
      ],
      password: [
        { value: data.source.password, disabled: data.isEdit },
        [Validators.required, Validators.pattern(STRONGPASSWORDPATTERN)],
      ],
      role: [data.source.role || USER_ROLES.ADMIN],
      shift: [data.source.shift, Validators.required],
      departments: [data.source.departments, Validators.required],
    });

    this.allDepartments = data.departmentList || [];
    this.shiftList = data.shiftList || [];
    this.shifts = data.shiftList || [];
  }

  // Getter for easy access to the departments FormControl
  get departments(): FormControl {
    return this.userForm.get('departments') as FormControl;
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      const userData = prepareRequestObject(this.userForm.value);

      this.dialogRef.close({
        action: this.data.isEdit ? 'update' : 'save',
        data: userData,
      });
    }
  }

  onDelete(): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Confirmation',
        message: 'Are you sure you want to delete this user?',
        cancelButtonName: 'Cancel',
        okButtonName: 'Ok',
      },
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        this.dialogRef.close({ action: 'delete', data: this.data.source });
      }
    });
  }
  onDepartmentChange(event: any): void {
    this.shiftList = this.shifts.filter((shift: IShift) => {
      return shift.departments.some((dept: string) =>
        event.value.includes(dept)
      );
    });
  }
  dismiss(): void {
    this.dialogRef.close(null);
  }
}

function prepareRequestObject(data: any): Partial<IUser> {
  return {
    firstname: data.firstName,
    lastname: data.lastName,
    phone: data.phoneNo,
    email: data.email,
    username: data.userName,
    password: data.password,
    shifts: [data.shift],

    role: data.role || USER_ROLES.ADMIN,
    departments: data.departments || [],
  };
}
