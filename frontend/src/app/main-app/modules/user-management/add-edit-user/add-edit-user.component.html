<label>
  {{ data.title }}
</label>

<div class="modal-content">
  <!-- Dialog Content -->
  <div mat-dialog-content>
    <form [formGroup]="userForm" class="form-grid">
      <div class="user-control-ele">
        <label class="form-label"
          >First Name <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>person</mat-icon>
          </span>
          <input
            matInput
            formControlName="firstName"
            placeholder="First Name"
          />
          <mat-error *ngIf="userForm.get('firstName')?.hasError('required')">
            First Name is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Last Name <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>person</mat-icon>
          </span>
          <input matInput formControlName="lastName" placeholder="Last Name" />
          <mat-error *ngIf="userForm.get('lastName')?.hasError('required')">
            Last Name is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Phone No. <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>phone</mat-icon>
          </span>
          <input matInput formControlName="phoneNo" placeholder="Phone No." />
          <mat-error *ngIf="userForm.get('phoneNo')?.hasError('required')">
            Phone Number is required
          </mat-error>
          <mat-error *ngIf="userForm.get('phoneNo')?.hasError('pattern')">
            Invalid phone number
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Email Address <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>email</mat-icon>
          </span>
          <input
            matInput
            formControlName="email"
            placeholder="Email Address @awm.com"
          />
          <mat-error *ngIf="userForm.get('email')?.hasError('required')">
            Email Address is required
          </mat-error>

          <mat-error *ngIf="userForm.get('email')?.hasError('pattern')">
            Please enter a valid email address
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Username <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>person</mat-icon>
          </span>
          <input matInput formControlName="userName" placeholder="Username" />
          <mat-error
            *ngIf="
              userForm.get('userName')?.touched &&
              userForm.get('userName')?.errors as errors
            "
          >
            <ng-container *ngIf="errors['required']">
              Username is required
            </ng-container>
            <ng-container *ngIf="!errors['required'] && errors['minlength']">
              Username must be at least 3 characters
            </ng-container>
            <ng-container
              *ngIf="
                !errors['required'] && !errors['minlength'] && errors['pattern']
              "
            >
              Username must be alphanumeric
            </ng-container>
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Password <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>lock</mat-icon>
          </span>
          <input
            matInput
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Password"
          />
          <button
            mat-icon-button
            matSuffix
            [disabled]="data.isEdit"
            (click)="hidePassword = !hidePassword"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hidePassword"
          >
            <mat-icon>{{
              hidePassword ? 'visibility_off' : 'visibility'
            }}</mat-icon>
          </button>
          <mat-error *ngIf="userForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
          <mat-error *ngIf="userForm.get('password')?.hasError('pattern')">
            Password must include at least one uppercase letter, one number, and
            one special character.
          </mat-error>
        </mat-form-field>
      </div>

      <div
        class="user-control-ele full-width-field"
        *ngIf="currentUserRole === 'admin'"
      >
        <label class="form-label"
          >Select User Role<span class="required">*</span></label
        >
        <mat-radio-group formControlName="role" class="user-role-radio-group">
          <mat-radio-button *ngFor="let role of userRoles" [value]="role[0]">
            {{ role[1] }}
          </mat-radio-button>
        </mat-radio-group>
        <mat-error *ngIf="userForm.get('userRole')?.hasError('required')">
          User role selection is required.
        </mat-error>
      </div>
      <div class="user-control-ele">
        <label class="form-label"
          >Select Department(s) <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-select
            formControlName="departments"
            (selectionChange)="onDepartmentChange($event)"
            multiple
          >
            <mat-option
              *ngFor="let department of allDepartments"
              [value]="department.id"
              >{{ department.name }}</mat-option
            >
          </mat-select>
          <mat-error *ngIf="userForm.get('departments')?.hasError('required')"
            >Department selection is required.
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Select Shift <span class="required">*</span></label
        >
        <mat-form-field
          appearance="outline"
          class="full-width-field"
          [ngClass]="{
            'disabled-input': userForm.get('departments')?.value?.length === 0,
          }"
        >
          <mat-select
            formControlName="shift"
            [disabled]="userForm.get('departments')?.value?.length === 0"
          >
            <mat-option *ngFor="let shift of shiftList" [value]="shift.id">{{
              shift.name
            }}</mat-option>
          </mat-select>
          <mat-error *ngIf="userForm.get('shift')?.hasError('required')"
            >Shift selection is required.
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </div>
</div>
<div
  mat-dialog-actions
  class="modal-action"
  [ngClass]="{ 'edit-mode': data.isEdit }"
>
  <button
    *ngIf="data.isEdit"
    mat-stroked-button
    class="action-button danger-button"
    color="warn"
    (click)="onDelete()"
  >
    Delete
  </button>
  <div class="ri-btns">
    <button
      mat-stroked-button
      class="action-button"
      color="primary"
      (click)="dismiss()"
    >
      Cancel
    </button>
    <button
      mat-flat-button
      class="action-button"
      color="primary"
      tabindex="-1"
      [disabled]="!userForm.valid"
      (click)="onSubmit()"
    >
      {{ data.isEdit ? 'Update' : 'Save' }}
    </button>
  </div>
</div>
