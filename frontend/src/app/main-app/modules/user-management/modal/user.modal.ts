export interface IUserResponse {
  status: boolean;
  data: IUser[];
  error_code: string | null;
  errors: any[];
  message: string;
}

export interface IUserDialogResult {
  action: 'save' | 'update' | 'delete';
  data: IUser;
}

export interface IUserDialogData {
  departmentList: IUserDepartment[];
  title: string;
  isEdit: boolean;
  source: IUserSource;
  maxWidth?: string;
  width?: string;
  panelClass?: string;
}

export interface IUser {
  id: number;
  firstname: string;
  lastname: string;
  phone: string;
  email: string;
  username: string;
  role: string;
  departments: string[];
  shifts: string[];
  password?: string; // Optional for edit operations
}

export interface IUserDepartment {
  id: string;
  name: string;
}

export interface IUserSource {
  id: number | string;
  name: string;
  startTime: string;
  endTime: string;
  format24hr: boolean;
  departments: string[];
}
