<div class="container">
  <div class="title">
    <label>
      {{ currentUserRole === 'admin' ? 'User List' : 'Manager List' }}
      <span class="badge">{{ dataSource.data.length }}</span>
    </label>
  </div>

  <div class="content-area">
    <div class="actions-container">
      <mat-form-field appearance="outline" class="search-field">
        <input matInput (keyup)="onSearch(input)" placeholder="Search" #input />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <button
        mat-flat-button
        color="primary"
        class="no-round"
        (click)="onAddUser()"
      >
        <mat-icon>add</mat-icon>
        Add User
      </button>
    </div>

    <!-- Angular Material Data Table -->
    <div class="table-container">
      <cnc-loader [show]="loader" />
      <table
        mat-table
        [dataSource]="dataSource"
        matSort
        class="mat-elevation-z0 user-list-table"
      >
        <!-- First Name Column -->
        <ng-container matColumnDef="firstname">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>First Name</th>
          <td mat-cell *matCellDef="let row">{{ row.firstname }}</td>
        </ng-container>

        <!-- Last Name Column -->
        <ng-container matColumnDef="lastname">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Name</th>
          <td mat-cell *matCellDef="let row">{{ row.lastname }}</td>
        </ng-container>

        <!-- Phone No. Column -->
        <ng-container matColumnDef="phone">
          <th mat-header-cell *matHeaderCellDef>Phone No.</th>
          <td mat-cell *matCellDef="let row">{{ row.phone }}</td>
        </ng-container>

        <!-- Email Address Column -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Email Address
          </th>
          <td mat-cell *matCellDef="let row">{{ row.email }}</td>
        </ng-container>

        <!-- User Name Column -->
        <ng-container matColumnDef="username">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>User Name</th>
          <td mat-cell *matCellDef="let row">{{ row.username }}</td>
        </ng-container>

        <!-- User Role Column -->
        <ng-container matColumnDef="role">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>User Role</th>
          <td mat-cell *matCellDef="let row">{{ row.role }}</td>
        </ng-container>

        <!-- Department Column -->
        <ng-container matColumnDef="departments">
          <th mat-header-cell *matHeaderCellDef>Department</th>
          <td mat-cell *matCellDef="let row">
            <span
              class="dept-name"
              matTooltip="{{ getDepartmentName(row.departments) || '' }}"
              >{{ getDepartmentName(row.departments) }}</span
            >
            <a
              [matMenuTriggerFor]="menu"
              class="more-link"
              *ngIf="row.departments?.length > 1"
            >
              + {{ row.departments?.length - 1 }} more
            </a>
            <mat-menu #menu="matMenu">
              <div class="dropmenu-dept">
                <li *ngFor="let dept of row.departments">
                  <a class="profile-link">{{ getDepartmentName(dept) }}</a>
                </li>
              </div>
            </mat-menu>
          </td>
        </ng-container>

        <!-- Shift Column -->
        <ng-container matColumnDef="shift">
          <th mat-header-cell *matHeaderCellDef>Shift</th>
          <td mat-cell *matCellDef="let row">
            {{ getShiftName(row.shifts) }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          (click)="onRowDoubleClick(row)"
          [ngClass]="{ 'selected-row': selectedRow?.id === row.id }"
        ></tr>

        <!-- Row shown when there is no matching data. -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="8">
            No data matching the filter "{{ input.value }}"
          </td>
        </tr>
      </table>
    </div>

    <!-- Paginator -->
    <mat-paginator
      #paginator
      [pageIndex]="0"
      [pageSize]="5"
      [pageSizeOptions]="[5, 10, 15, 20]"
      showFirstLastButtons
    >
    </mat-paginator>
  </div>
</div>
