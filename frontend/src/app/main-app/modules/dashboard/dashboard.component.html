<ng-container *ngIf="fullScreenMode">
  <cnc-live-dashboard> </cnc-live-dashboard>
</ng-container>
<ng-container *ngIf="!fullScreenMode">
  <div class="dashboard-content">
    <div class="tabs">
      <div
        class="tab"
        (click)="onTabClick(0)"
        [ngClass]="{ active: selectedTab === 0 }"
      >
        <label for="live-dashboard">Live Dashboard</label>
      </div>
      <div
        class="tab"
        (click)="onTabClick(1)"
        [ngClass]="{ active: selectedTab === 1 }"
      >
        <label for="history">History </label>
      </div>
    </div>

    <div class="content">
      <ng-container *ngIf="selectedTab === 0">
        <cnc-live-dashboard> </cnc-live-dashboard>
      </ng-container>
      <ng-container *ngIf="selectedTab === 1">
        <cnc-history-dashboard> </cnc-history-dashboard>
      </ng-container>
    </div>
  </div>
</ng-container>
