import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardComponent } from './dashboard.component';
import { DashboardRoutingModule } from './dashboard.routing.module';
import { MainAppModule } from '../../main-app.module'; // Adjust the import path as necessary
import { LiveDashboardComponent } from './components/live-dashboard/live-dashboard.component';
import { HistoryDashboardComponent } from './components/history-dashboard/history-dashboard.component';
import { CoreModule } from '../../../core/core.module';
import { SharedModule } from '../../../shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { NgChartsModule } from 'ng2-charts';
import { MachineStatusComponent } from './components/machine-status/machine-status.component';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
@NgModule({
  declarations: [
    DashboardComponent,
    LiveDashboardComponent,
    HistoryDashboardComponent,
    MachineStatusComponent,
  ],
  imports: [
    CommonModule,
    DashboardRoutingModule,
    CoreModule,
    SharedModule,
    MainAppModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatSelectModule,
    NgChartsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
  ],
})
export class DashboardModule {}
