import { Component } from '@angular/core';
import { SettingsService } from '../settings/settings.service';

@Component({
  selector: 'cnc-dashboard',
  standalone: false,
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent {
  selectedTab = 0;
  fullScreenMode = false;
  constructor(private settingsService: SettingsService) {
    this.settingsService.fullScreenMode$.subscribe(
      (mode) => (this.fullScreenMode = mode)
    );
  }
  onTabClick(args: number) {
    this.selectedTab = args;
  }
}
