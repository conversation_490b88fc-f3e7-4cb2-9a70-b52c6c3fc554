.dashboard-content {
  position: relative;
  height: 86vh;
}

.content {
  height: 80vh;
}
.tabs {
  display: flex;
  margin-top: 20px;
  width: 400px;
  position: relative;
  z-index: 500;
}

.tab {
  padding: 10px 20px;
  border-bottom: 1px solid;
  border-bottom-color: var(
    --mat-tab-header-divider-color,
    var(--mat-sys-surface-variant)
  );
  cursor: pointer;
}
.tab label {
  cursor: pointer;
}

.tab.active {
  border-bottom: 3px solid var(--mat-sys-primary);
}
