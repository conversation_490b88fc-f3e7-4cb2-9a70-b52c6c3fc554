.wrapper {
  display: grid;
  grid-template-rows: 1fr 50px;
  height: 80vh;
}
.panel-filter {
  display: flex;
  justify-content: flex-end;
  gap: 14px;
  margin-top: -24px;
  margin-bottom: -17px;
  position: absolute;
  width: -webkit-fill-available;
  top: 21px;
  padding-left: 30%;
}
.date-range-field {
  width: 316px;
}
.table-container {
  min-height: 400px;
  position: relative;
  margin-top: 10px;
  overflow-y: scroll;
}
table {
  border-color: var(
    --mat-table-row-item-outline-color,
    var(--mat-sys-outline, rgba(0, 0, 0, 0.12))
  );
  border-style: solid;
  border-width: 1px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

::ng-deep .history-table-container table thead {
  position: sticky;
  top: 0;
}

.mat-column-chart {
  text-align: center;
}

::ng-deep .history-table thead th {
  background-color: var(--mat-sys-primary) !important;
  color: var(--mat-sys-inverse-on-surface);
}
::ng-deep .history-table thead th:first-child {
  border-top-left-radius: 10px;
}
::ng-deep .history-table thead th:last-child {
  border-top-right-radius: 10px;
}

::ng-deep .history-table tr {
  cursor: pointer;
}

.mat-column-spacer,
.mat-column-spacer2 {
  width: 9px;
  background-color: var(--mat-sys-surface-container-low) !important;
  border: 0px;
  position: relative;
  top: -1px;
  padding: 0px;
  border-left: 1px solid;
  border-right: 1px solid;
  border-color: var(
    --mat-table-row-item-outline-color,
    var(--mat-sys-outline, rgba(0, 0, 0, 0.12))
  );
}

.utrate {
  border-width: 1px;
  border-style: solid;
  padding: 5px 12px;
  border-radius: 25px;
  font-weight: 500;
  color: #000;
}
.utrate.success,
.chart-icon.success {
  background-color: #bff5bf; /* Green */
  border-color: #009600;
}
.utrate.warning-light,
.chart-icon.warning-light {
  background-color: #f6f1bf; /* Yellow */
  border-color: #d7d700;
}
.utrate.warning-dark,
.chart-icon.warning-dark {
  background-color: #f3dfc0; /* Red */
  border-color: #ffa500;
}
.utrate.danger,
.chart-icon.danger {
  background-color: #f4c6c0; /* Red */
  border-color: #ff1b1b;
}

.chart-icon.success img {
  filter: brightness(0) saturate(100%) invert(35%) sepia(99%) saturate(1533%)
    hue-rotate(95deg) brightness(96%) contrast(101%);
}
.chart-icon.warning-light img {
  filter: brightness(0) saturate(100%) invert(85%) sepia(53%) saturate(4359%)
    hue-rotate(359deg) brightness(91%) contrast(85%);
}
.chart-icon.warning-dark img {
  filter: brightness(0) saturate(100%) invert(73%) sepia(99%) saturate(937%)
    hue-rotate(357deg) brightness(101%) contrast(102%);
}
.chart-icon.danger img {
  filter: brightness(0) saturate(100%) invert(26%) sepia(83%) saturate(5647%)
    hue-rotate(351deg) brightness(100%) contrast(103%);
}

.chart-icon {
  background-color: red;
  padding: 7px;
  border-radius: 7px;
  padding-bottom: 9px;
  border-width: 1px;
  border-style: solid;
}

.chart-icon img {
  width: 20px;
  height: 16px;
  vertical-align: middle;
}
