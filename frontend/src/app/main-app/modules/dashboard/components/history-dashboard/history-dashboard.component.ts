import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { HistoryDashboardService } from './services/history-dashboard.service';
import { merge, of as observableOf, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  switchMap,
  takeUntil,
} from 'rxjs/operators';
import { ShiftManagementService } from '../../../shift-management/services/shift-management.service';
import { FormControl, FormGroup } from '@angular/forms';
import { DATE_MONTH_NAME_FORMAT } from '../../../../../app.constant';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';
import { MatDialog } from '@angular/material/dialog';
import { MachineStatusComponent } from '../machine-status/machine-status.component';
import { machineStatusMock } from '../mockdata';
@Component({
  selector: 'cnc-history-dashboard',
  standalone: false,
  templateUrl: './history-dashboard.component.html',
  styleUrl: './history-dashboard.component.scss',
  providers: [provideMomentDateAdapter(DATE_MONTH_NAME_FORMAT)],
})
export class HistoryDashboardComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  loader = false;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>([]);
  displayedColumns: string[] = [
    'transmitterName',
    'utilizationRate',
    'runTime',
    'idleTime',
    'downTime',
    'spacer',
    'alerts',
    'lastOccurrence',
    'alertDuration',
    'spacer2',
    'chart',
  ];

  selectedRow: any = null;
  private destroy$ = new Subject<void>(); // For unsubscribing observables
  private destroy2$ = new Subject<void>(); // For unsubscribing observables
  allDepartments: any = [];
  selectedDepartment = '';
  todateDate: Date = new Date();
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  today = new Date();
  readonly range = new FormGroup({
    start: new FormControl<Date | null>(new Date()),
    end: new FormControl<Date | null>(
      new Date(this.today.setDate(this.today.getDate() + 7))
    ),
  });

  constructor(
    private historyDashboardService: HistoryDashboardService,
    private shiftManagementService: ShiftManagementService,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef // Inject ChangeDetectorRef
  ) {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  ngOnInit(): void {
    this.shiftManagementService
      .getDepartments()
      .subscribe((departments: any) => {
        this.allDepartments = departments.data;
        if (this.allDepartments.length > 0) {
          this.selectedDepartment = this.allDepartments[0].id;
        }
      });
    this.dateRangeChanged();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    if (!this.paginator) {
      return;
    }
    if (!this.sort) {
      return;
    }

    this.sort.sortChange
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => (this.paginator.pageIndex = 0));

    merge(this.sort.sortChange, this.paginator.page)
      .pipe(
        startWith({}), // Emit an initial value to trigger the first data load
        switchMap(() => {
          this.loader = true;
          this.cdr.detectChanges(); // Trigger change detection
          const pageIndex = this.paginator.pageIndex;
          const pageSize = this.paginator.pageSize;
          const sortBy = this.sort.active || ''; // Use empty string if not sorted
          const sortDirection = this.sort.direction || ''; // Use empty string if not sorted

          return this.historyDashboardService
            .getHistoryData(pageIndex, pageSize, sortBy, sortDirection)
            .pipe(catchError(() => observableOf(null))); // Handle errors gracefully
        }),
        map((apiResponse) => {
          this.loader = false;
          const { data } = apiResponse;
          const [{ records, total }] = data || [];
          if (records.length === 0) {
            return { data: [], totalCount: 0 };
          }
          // this.totalItems = apiResponse.totalCount;
          return { data: records, totalCount: total };
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((result) => {
        this.dataSource.data = result.data;
        this.paginator.length = result.totalCount;
      });
  }

  getHistoryData() {
    this.loader = true;
    this.historyDashboardService
      .getHistoryData(
        this.paginator.pageIndex,
        this.paginator.pageSize,
        this.sort.active,
        this.sort.direction
      )
      .subscribe({
        next: (res: any) => {
          this.loader = false;
          const { data } = res;
          const [{ records, total }] = data || [];
          this.dataSource.data = records;
          this.paginator.length = total;
        },
      });
  }

  onRowSelect(args: any) {
    this.selectedRow = args;
  }

  dateRangeChanged() {
    this.range.valueChanges
      .pipe(debounceTime(200), takeUntil(this.destroy2$)) // Unsubscribe when component is destroyed
      .subscribe((dateRange: any) => {
        // This callback will be triggered whenever the start or end date changes
        // console.log('Date range changed:', dateRange);
        // You can now access dateRange.start and dateRange.end
        if (dateRange.start && dateRange.end) {
          // console.log('Start Date:', dateRange.start._d);
          // console.log('End Date:', dateRange.end._d);
          // Perform your desired actions here, e.g., filter data, make API calls
        }
      });
  }

  onViewChartClick() {
    const dialogRef = this.dialog.open(MachineStatusComponent, {
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
      data: machineStatusMock,
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroy2$.next();
    this.destroy2$.complete();
  }
}
