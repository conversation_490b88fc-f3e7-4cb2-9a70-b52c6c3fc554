import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { HistoryDashboardComponent } from './history-dashboard.component';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { CoreModule } from '../../../../../core/core.module';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { SharedModule } from '../../../../../shared/shared.module';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('HistoryDashboardComponent', () => {
  let component: HistoryDashboardComponent;
  let fixture: ComponentFixture<HistoryDashboardComponent>;

  // Use fakeAsync to control the timing of asynchronous operations within the test.
  beforeEach(fakeAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CoreModule,
        ReactiveFormsModule,
        MatDatepickerModule,
        MatSelectModule,
        SharedModule,
        MatTableModule,
        MatSortModule,
        MatPaginatorModule,
        // Import NoopAnimationsModule to disable animations which can interfere with tests.
        NoopAnimationsModule,
      ],
      declarations: [HistoryDashboardComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryDashboardComponent);
    component = fixture.componentInstance;

    // The first fixture.detectChanges() is necessary to trigger ngOnInit and ngAfterViewInit.
    // This is where the component's initial data loading logic begins.
    fixture.detectChanges();

    // In ngAfterViewInit, an observable with `startWith({})` immediately emits,
    // causing a state change (`loader = true`). This happens within the same
    // change detection cycle, leading to the ExpressionChangedAfterItHasBeenCheckedError.
    // Calling tick() flushes the microtask queue, allowing the observable to
    // complete its synchronous emission and state update.
    tick();

    // A second fixture.detectChanges() is called to apply the updated state
    // (e.g., the loader being visible) to the component's view. This stabilizes
    // the component before the test assertion runs.
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
