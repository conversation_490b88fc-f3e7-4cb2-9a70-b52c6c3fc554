import { Injectable } from '@angular/core';
import { delay, map, Observable, of } from 'rxjs';
import { userUrl } from '../../../../../../app.constant';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class HistoryDashboardService {
  constructor(private http: HttpClient) {}

  getHistoryData(
    pageIndex: number,
    pageSize: number,
    sortBy: string,
    sortDirection: 'asc' | 'desc' | ''
  ): Observable<any> {
    const params: any = {
      page: pageIndex + 1, // API usually expects 1-based page index
      limit: pageSize,
      sortBy: sortBy,
      sortDirection: sortDirection,
    };

    // Example: append query parameters to the URL
    const queryString = Object.keys(params)
      .filter(
        (key) =>
          params[key] !== undefined &&
          params[key] !== null &&
          params[key] !== ''
      )
      .map(
        (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
      )
      .join('&');

    const fullUrl = `${userUrl}list?${queryString}`;

    return of({
      data: [
        {
          records: [
            {
              transmitterName: 'Transmitter 1',
              utilizationRate: 75,
              runTime: 120,
              idleTime: 30,
              downTime: 10,
              alerts: 5,
              lastOccurrence: '2023-10-01',
              alertDuration: 15,
              chart: 'Chart 1',
            },
            {
              transmitterName: 'Transmitter 2',
              utilizationRate: 85,
              runTime: 150,
              idleTime: 20,
              downTime: 5,
              alerts: 2,
              lastOccurrence: '2023-10-02',
              alertDuration: 10,
              chart: 'Chart 2',
            },
            {
              transmitterName: 'Transmitter 3',
              utilizationRate: 65,
              runTime: 100,
              idleTime: 40,
              downTime: 15,
              alerts: 3,
              lastOccurrence: '2023-10-03',
              alertDuration: 20,
              chart: 'Chart 3',
            },
            {
              transmitterName: 'Transmitter 4',
              utilizationRate: 90,
              runTime: 180,
              idleTime: 10,
              downTime: 2,
              alerts: 1,
              lastOccurrence: '2023-10-04',
              alertDuration: 5,
              chart: 'Chart 4',
            },
            {
              transmitterName: 'Transmitter 5',
              utilizationRate: 70,
              runTime: 110,
              idleTime: 35,
              downTime: 12,
              alerts: 4,
              lastOccurrence: '2023-10-05',
              alertDuration: 12,
              chart: 'Chart 5',
            },
            {
              transmitterName: 'Transmitter 6',
              utilizationRate: 80,
              runTime: 140,
              idleTime: 25,
              downTime: 8,
              alerts: 2,
              lastOccurrence: '2023-10-06',
              alertDuration: 8,
              chart: 'Chart 6',
            },
            {
              transmitterName: 'Transmitter 7',
              utilizationRate: 60,
              runTime: 90,
              idleTime: 50,
              downTime: 20,
              alerts: 6,
              lastOccurrence: '2023-10-07',
              alertDuration: 25,
              chart: 'Chart 7',
            },
            {
              transmitterName: 'Transmitter 8',
              utilizationRate: 95,
              runTime: 200,
              idleTime: 5,
              downTime: 1,
              alerts: 0,
              lastOccurrence: '2023-10-08',
              alertDuration: 2,
              chart: 'Chart 8',
            },
            {
              transmitterName: 'Transmitter 9',
              utilizationRate: 78,
              runTime: 130,
              idleTime: 28,
              downTime: 9,
              alerts: 3,
              lastOccurrence: '2023-10-09',
              alertDuration: 14,
              chart: 'Chart 9',
            },
            {
              transmitterName: 'Transmitter 10',
              utilizationRate: 82,
              runTime: 160,
              idleTime: 18,
              downTime: 6,
              alerts: 1,
              lastOccurrence: '2023-10-10',
              alertDuration: 7,
              chart: 'Chart 10',
            },
          ],
          total: 10,
        },
      ],
    }).pipe(
      delay(2000),
      map((response) => {
        response.data[0].records.forEach((item: any) => {
          item.utilizationRateClass = utilizationRateColorClass(
            item.utilizationRate
          );
        });
        return response;
      })
    ); // Simulating a delay for demonstration purposes
    return this.http.get<any>(fullUrl);
  }
}

function utilizationRateColorClass(rate: number): string {
  if (rate >= 80) {
    return 'success';
  } else if (rate >= 50) {
    return 'warning-light';
  } else if (rate >= 30) {
    return 'warning-dark';
  } else {
    return 'danger';
  }
}
