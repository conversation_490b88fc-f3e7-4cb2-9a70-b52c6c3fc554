import { TestBed } from '@angular/core/testing';

import { HistoryDashboardService } from './history-dashboard.service';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('HistoryDashboardService', () => {
  let service: HistoryDashboardService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(HistoryDashboardService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
