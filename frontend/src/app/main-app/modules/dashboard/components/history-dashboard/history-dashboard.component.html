<div class="panel-filter">
  <mat-form-field class="date-range-field">
    <mat-date-range-input
      [formGroup]="range"
      [rangePicker]="picker"
      [disabled]="true"
    >
      <input matStartDate formControlName="start" placeholder="Start date" />
      <input matEndDate formControlName="end" placeholder="End date" />
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
    <mat-date-range-picker #picker></mat-date-range-picker>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width-field disabled-input">
    <mat-select [value]="selectedDepartment" [disabled]="true">
      <mat-option
        *ngFor="let department of allDepartments"
        [value]="department.id"
        >{{ department.name }}</mat-option
      >
    </mat-select>
  </mat-form-field>
</div>
<div class="wrapper">
  <div class="table-container history-table-container">
    <cnc-loader [show]="loader" />
    <table
      mat-table
      [dataSource]="dataSource"
      matSort
      class="mat-elevation-z0 history-table"
    >
      <!-- Transmitter Name Column -->
      <ng-container matColumnDef="transmitterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          Transmitter Name
        </th>
        <td mat-cell *matCellDef="let row">{{ row.transmitterName }}</td>
      </ng-container>

      <!-- Utilization Rate Column -->
      <ng-container matColumnDef="utilizationRate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          Utilization Rate
        </th>
        <td mat-cell *matCellDef="let row">
          <span class="utrate {{ row.utilizationRateClass }}"
            >{{ row.utilizationRate }} %</span
          >
        </td>
      </ng-container>

      <!-- runTime Column -->
      <ng-container matColumnDef="runTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Run Time</th>
        <td mat-cell *matCellDef="let row">{{ row.runTime }}</td>
      </ng-container>

      <!-- idleTime Column -->
      <ng-container matColumnDef="idleTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Idle Time</th>
        <td mat-cell *matCellDef="let row">{{ row.idleTime }}</td>
      </ng-container>

      <!-- downTime Column -->
      <ng-container matColumnDef="downTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Down Time</th>
        <td mat-cell *matCellDef="let row">{{ row.downTime }}</td>
      </ng-container>

      <ng-container matColumnDef="spacer">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element"></td>
      </ng-container>

      <!-- alerts Column -->
      <ng-container matColumnDef="alerts">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Alerts</th>
        <td mat-cell *matCellDef="let row">{{ row.alerts }}</td>
      </ng-container>

      <!-- lastOccurrence Column -->
      <ng-container matColumnDef="lastOccurrence">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          Last Occurrence
        </th>
        <td mat-cell *matCellDef="let row">{{ row.lastOccurrence }}</td>
      </ng-container>

      <!-- alertDuration Column -->
      <ng-container matColumnDef="alertDuration">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          Alert Duration
        </th>
        <td mat-cell *matCellDef="let row">{{ row.alertDuration }}</td>
      </ng-container>

      <ng-container matColumnDef="spacer2">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element"></td>
      </ng-container>

      <!-- chart Column -->
      <ng-container matColumnDef="chart">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          Vistualization
        </th>
        <td mat-cell *matCellDef="let row">
          <span
            class="chart-icon {{ row.utilizationRateClass }}"
            (click)="onViewChartClick()"
          >
            <img src="icons/et_bargraph.svg" alt="Bar Graph Icon" />
          </span>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: displayedColumns"
        (click)="onRowSelect(row)"
        [ngClass]="{ 'selected-row': selectedRow?.id === row.id }"
      ></tr>

      <!-- Row shown when there is no matching data. -->
      <tr class="mat-row" *matNoDataRow>
        <td class="mat-cell" colspan="8">No data found</td>
      </tr>
    </table>
  </div>

  <div class="paginator-container">
    <!-- Paginator -->
    <mat-paginator
      #paginator
      [pageIndex]="0"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 15, 20]"
      showFirstLastButtons
    >
    </mat-paginator>
  </div>
</div>
