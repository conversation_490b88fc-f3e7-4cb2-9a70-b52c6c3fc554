import { AfterViewInit, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ChartOptions, ChartType } from 'chart.js';
import * as d3 from 'd3';
import { MachineStatusData } from '../modal/livedashboard';

@Component({
  selector: 'cnc-machine-status',
  standalone: false,
  templateUrl: './machine-status.component.html',
  styleUrl: './machine-status.component.scss',
})
export class MachineStatusComponent implements AfterViewInit, OnInit {
  transmitter: any;
  statusData: any[] = [];
  statusData1: any[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MachineStatusData,
    public dialogRef: MatDialogRef<MachineStatusComponent>
  ) {}
  ngOnInit(): void {
    this.transmitter = this.data.transmitter;
    this.statusData = this.data.statusData;
    this.statusData1 = this.data.statusData1;
  }

  public doughnutChartType: any = 'doughnut';
  public doughnutChartData = {
    labels: ['Run Time', 'Idle Time', 'Down Time'],
    datasets: [
      {
        data: [30, 30, 40],
        backgroundColor: ['#009600', '#FFB81C', '#960000'],
        borderWidth: 0,
      },
    ],
  };

  public doughnutChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
    cutout: '90%',
  };

  ngAfterViewInit(): void {
    this.drawTimeline();

    // Hide tooltip on outside click
    d3.select('body').on('click', () => {
      d3.select('#tooltip').style('display', 'none');
    });
  }

  drawTimeline(): void {
    const svgWidth = 700;
    const svgHeight = 160;
    const hourWidth = 80;
    const barHeight = 60;

    const svg = d3
      .select('#timelineChart')
      .append('svg')
      .attr('width', svgWidth)
      .attr('height', svgHeight);

    let x = 0;

    this.statusData1.forEach((hour) => {
      const hourGroup = svg.append('g').attr('transform', `translate(${x}, 0)`);
      let offset = 0;

      hour.blocks.forEach(
        (block: {
          duration: number;
          color:
            | string
            | number
            | boolean
            | readonly (string | number)[]
            | d3.ValueFn<
                SVGRectElement,
                unknown,
                string | number | boolean | readonly (string | number)[] | null
              >
            | null;
          status: any;
        }) => {
          const blockWidth = (block.duration / 60) * hourWidth;

          hourGroup
            .append('rect')
            .attr('x', offset)
            .attr('y', 0)
            .attr('width', blockWidth)
            .attr('height', barHeight)
            .attr('fill', block.color)
            .style('cursor', 'pointer')
            .on('click', function (event) {
              const [mouseX, mouseY] = d3.pointer(event);

              d3
                .select('#tooltip')
                .style('display', 'block')
                .style(
                  'left',
                  `${event.pageX > 800 ? event.pageX - 522 : event.pageX - 290}px`
                )
                .style('top', `${event.pageY - 60}px`) // 50px above click position
                .html(`
      <strong>Alert occurred:</strong> ${block.status}<br/>
      <strong>Alert Duration:</strong> ${block.duration} min<br/>
    `);

              event.stopPropagation();
            });

          offset += blockWidth;
        }
      );

      // Add time label below the bar
      hourGroup
        .append('text')
        .attr('x', 0)
        .attr('y', barHeight + 15)
        .text(hour.time)
        .attr('font-size', '10px');

      x += hourWidth;
    });

    // Legend
    const legend = [
      { label: 'Run Time', color: 'green' },
      { label: 'Idle Time', color: 'gray' },
      { label: 'Downtime Time', color: 'orange' },
      { label: 'Breakdown', color: 'red' },
    ];

    const legendGroup = svg
      .append('g')
      .attr('transform', `translate(0, ${barHeight + 40})`);

    legend.forEach((item, i) => {
      const xOffset = i * 150;

      legendGroup
        .append('circle')
        .attr('cx', xOffset + 6)
        .attr('cy', 6)
        .attr('r', 6)
        .attr('fill', item.color);

      legendGroup
        .append('text')
        .attr('x', xOffset + 18)
        .attr('y', 10)
        .text(item.label)
        .attr('font-size', '12px');
    });
  }

  dismiss(): void {
    this.dialogRef.close(null);
  }
}
