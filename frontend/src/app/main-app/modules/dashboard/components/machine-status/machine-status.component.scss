.status-header {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  padding: 16px;
  flex-wrap: wrap;
}

.info-card {
  flex: 1;
  background-color: #1b9e77;
  color: var(--mat-body-text);
  border-radius: 8px;
  padding: 16px;
  min-width: 240px;
  font-size: 14px;

  p {
    margin: 8px 0;
  }
}

.status-card {
  flex: 2;
  display: flex;
  align-items: center;
  background-color: var(--mat-card-background-color);
  border-radius: 8px;
  border: 1px solid #009600;
  padding: 16px;
  gap: 24px;
  min-width: 320px;

  .chart-section {
    flex: 0 0 110px;

    .chart-container {
      width: 110px;
      height: 110px;
      position: relative;

      canvas {
        width: 100% !important;
        height: 100% !important;
      }

      .chart-center-label {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 16px;
        font-weight: bold;
        color: var(--mat-body-text);
      }
    }
  }

  .details-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .date-picker {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
    }

    .status-list {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .status-item {
        display: flex;
        align-items: center;

        .status-icon {
          font-size: 20px;
          margin-right: 8px;
        }

        .status-label-value {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .status-label {
          font-weight: 600;
        }

        .status-value {
          margin-left: 2rem;
          font-weight: 500;
        }
      }
    }
  }
}
#timelineChart {
  position: relative;
  margin-left: 1rem;
  margin-top: 1rem;
}
.machinestatus {
  border-radius: 8px;
  border: 1px solid #009600;
  padding: 10px;
  width: 100%;
}
.machineName {
  font-family: Montserrat;
  font-size: 24px;
}
#tooltip {
  position: absolute;
  display: none;
  background-color: var(--mat-sys-surface);
  border: 1px solid var(--mat-sys-surface);
  padding: 8px;
  font-size: 12px;
  border-radius: 4px;
  pointer-events: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}
.title {
  padding: 15px 0px;
  font-size: 18px;
  font-family: MontserratSemiBold;
}
.right-actions {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 0;
  margin-left: auto;
}
.action-button {
  min-width: 120px;
  font-weight: 500;
  font-size: 14px;
  height: 40px;
  border-radius: 8px;
  text-transform: none;
}
