<div class="status-header">
  <div class="info-card">
    <label class="machineName">Machine#</label>
    <p><strong>Signal Name:</strong> {{ transmitter.name }}</p>
    <p><strong>Transmitter ID:</strong> {{ transmitter.id }}</p>
    <p><strong>Department:</strong> {{ transmitter.department }}</p>
    <p><strong>Shift:</strong> {{ transmitter.shift }}</p>
    <p><strong>Current Operator:</strong> {{ transmitter.operator }}</p>
  </div>

  <div class="status-card">
    <div class="chart-section">
      <div class="chart-container">
        <canvas
          baseChart
          [data]="doughnutChartData"
          [options]="doughnutChartOptions"
          [type]="doughnutChartType"
        >
        </canvas>
        <div class="chart-center-label">50%</div>
      </div>
    </div>

    <div class="details-section">
      <div class="date-picker">
        <div class="status-date">
          {{ transmitter.date }}
        </div>
      </div>
      <div class="status-list">
        <div class="status-item" *ngFor="let item of statusData">
          <img
            class="status-icon"
            [src]="item.icon"
            [alt]="item.label + ' icon'"
          />
          <span class="status-label">{{ item.label }}</span>
          <span class="status-value">{{ item.value }} mins</span>
        </div>
      </div>
    </div>
  </div>
  <div class="machinestatus">
    <label class="title">Machine Status</label>
    <div id="tooltip"></div>
    <div id="timelineChart"></div>
  </div>

  <div class="right-actions">
    <button
      mat-stroked-button
      class="action-button"
      color="primary"
      (click)="dismiss()"
    >
      Cancel
    </button>
  </div>
</div>
