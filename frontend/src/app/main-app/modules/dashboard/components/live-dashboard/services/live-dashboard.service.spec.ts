import { TestBed } from '@angular/core/testing';

import { LiveDashboardService } from './live-dashboard.service';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('LiveDashboardService', () => {
  let service: LiveDashboardService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(LiveDashboardService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
