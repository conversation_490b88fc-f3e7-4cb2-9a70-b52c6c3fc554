import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LiveDashboardService {
  constructor(private http: HttpClient) {}

  getLiveDashbardData() {
    return of([
      {
        signalName: 'Alpha-01',
        transmitterName: 'Main Transmitter',
        transmitterId: 'bq500212A',
        status: 'Online',
        alerts: 1,
        headerColor: '#D90707',

        flash: true,
        graphData: {
          type: 'bar',
          values: [
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
          ],
        },
      },
      {
        signalName: 'Beta-02 (Inactive)',
        transmitterName: 'Backup Transmitter',
        transmitterId: 'bq500213B',
        status: 'Online',
        alerts: 1,
        headerColor: '#1B9E77',
        icon: 'server',
        graphData: { type: 'placeholder' },
      },
      {
        signalName: 'Gamma-03',
        transmitterName: 'Sector B Transmitter',
        transmitterId: 'bq500214C',
        status: 'Online',
        alerts: 1,
        headerColor: '#7570B3',
        icon: 'layers',
        graphData: {
          type: 'bar',
          values: [
            { color: 'green', height: 100 },
            { color: 'red', height: 25 },
            { color: 'green', height: 90 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
          ],
        },
      },
      {
        signalName: 'Delta-04',
        transmitterName: 'Primary Feed',
        transmitterId: 'bq500215D',
        status: 'Online',
        alerts: 1,
        headerColor: '#E7298A',
        icon: 'layers',
        graphData: {
          type: 'bar',
          values: [
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
          ],
        },
      },
      {
        signalName: 'Epsilon-05',
        transmitterName: 'High-Frequency Unit',
        transmitterId: 'bq500216E',
        status: 'Online',
        alerts: 1,
        headerColor: '#66A61E',
        icon: 'bolt',
        graphData: {
          type: 'bar',
          values: [
            { color: 'green', height: 100 },
            { color: 'green', height: 90 },
            { color: 'green', height: 95 },
            { color: 'green', height: 100 },
            { color: 'green', height: 88 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
          ],
        },
      },
      {
        signalName: 'Zeta-06 (CRITICAL)',
        transmitterName: 'Core System',
        transmitterId: 'bq500217F',
        status: 'Offline',
        alerts: 8,
        headerColor: '#E6AB02',
        icon: 'graph-bar',
        graphData: {
          type: 'bar',
          values: [
            { color: 'red', height: 100 },
            { color: 'red', height: 75 },
            { color: 'red', height: 100 },
            { color: 'red', height: 80 },
            { color: 'red', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
            { color: 'green', height: 100 },
            { color: 'green', height: 75 },
            { color: 'red', height: 50 },
            { color: 'green', height: 100 },
            { color: 'green', height: 80 },
            { color: 'red', height: 30 },
            { color: 'green', height: 100 },
          ],
        },
      },
      // Add more data objects here to create more tiles
    ]);
    // This is a placeholder URL, replace it with your actual API endpoint
    const url = 'https://api.example.com/live-dashboard-data';

    return this.http.get<any[]>(url);
  }
}
