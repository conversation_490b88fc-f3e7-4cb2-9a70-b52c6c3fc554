<div class="panel-block">
  <div class="panel-filter" *ngIf="!fullScreenMode">
    <mat-form-field>
      <input matInput [matDatepicker]="picker1" [value]="todateDate" disabled />
      <mat-datepicker-toggle
        matIconSuffix
        [for]="picker1"
      ></mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>

    <mat-form-field
      *ngIf="currentUserRole === 'superadmin'"
      appearance="outline"
      class="full-width-field disabled-input"
    >
      <mat-select [value]="selectedDepartment">
        <mat-option
          *ngFor="let department of allDepartments"
          [value]="department.id"
          disabled
          >{{ department.name }}</mat-option
        >
      </mat-select>
    </mat-form-field>

    <mat-form-field
      appearance="outline"
      class="full-width-field disabled-input"
    >
      <mat-select [value]="selectedTransmitter">
        <mat-option
          *ngFor="let transmitter of transmitterList"
          [value]="transmitter.id"
          disabled
          >{{ transmitter.name }}</mat-option
        >
      </mat-select>
    </mat-form-field>

    <mat-form-field
      appearance="outline"
      class="full-width-field disabled-input"
    >
      <span matPrefix class="prefix-icon" style="margin-left: 5px">
        <mat-icon>filter_alt</mat-icon>
      </span>
      <mat-select [value]="selectedSignal" disabled>
        <mat-option *ngFor="let signal of signalList" [value]="signal.id">{{
          signal.name
        }}</mat-option>
      </mat-select>
    </mat-form-field>

    <img
      (click)="onClickFullScreen()"
      src="icons/full-screen.svg"
      alt="Full Screen"
      class="full-screen-icon"
    />
  </div>

  <div class="container-area">
    @for (cardData of dataSource; track cardData) {
      <div class="card" [style.borderColor]="cardData.headerColor">
        <div class="card-header" [style.backgroundColor]="cardData.headerColor">
          <div>
            <h3>{{ cardData.signalName }}</h3>
            <p>{{ cardData.transmitterName }}</p>
          </div>
          <!-- You can add logic here to display different icons -->
          <img
            *ngIf="cardData.flash"
            src="icons/flash.svg"
            class="flash-icon"
            alt="Flash Icon"
          />
          <div class="icon-container" (click)="status()">
            <img src="icons/et_bargraph.svg" alt="Bar Graph Icon" />
          </div>
        </div>
        <div class="card-body">
          <div
            class="card-graph"
            [class.bar-placeholder]="cardData.graphData.type === 'placeholder'"
          >
            <!-- Loop through graph data if it exists -->
            <ng-container
              *ngIf="
                cardData.graphData.type === 'bar' && cardData.graphData.values
              "
            >
              <div class="timeline-bar">
                <div
                  *ngFor="let bar of cardData.graphData.values"
                  class="timeline-segment"
                  [style.backgroundColor]="bar.color"
                ></div>
              </div>
            </ng-container>
          </div>
          <div class="card-info">
            <div class="info-item">
              <p class="label">Transmitter ID</p>
              <p class="value">{{ cardData.transmitterId }}</p>
            </div>
            <div class="info-item">
              <p class="label">Status</p>
              <p class="value">{{ cardData.status }}</p>
            </div>
            <div class="info-item">
              <p class="label">Alerts</p>
              <p class="value">{{ cardData.alerts | number: '2.0-0' }}</p>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</div>
