.container-area {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  padding-top: 15px;
  justify-content: flex-start;
}

.panel-filter {
  display: flex;
  justify-content: flex-end;
  gap: 14px;
  margin-top: -24px;
  margin-bottom: -17px;
  position: absolute;
  width: -webkit-fill-available;
  top: 21px;
  padding-left: 30%;
}
.card {
  width: 250px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  color: white;
  position: relative;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.card-header p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
}

.card-icon {
  width: 24px;
  height: 24px;
}

.card-body {
  padding: 16px;
}

.card-graph {
  height: 40px;
  display: flex;
  gap: 2px;
  align-items: flex-end;
  margin-bottom: 16px;
}

.graph-bar {
  flex-grow: 1;
  border-radius: 2px;
}

.bar-placeholder {
  background-color: #e0e0e0;
  border-radius: 4px;
}

.card-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.info-item p {
  margin: 0;
}

.info-item .label {
  font-weight: 600;
  margin-bottom: 4px;
}

.icon-container {
  padding: 8px;
  border: 1px solid #fff;
  border-radius: 6px;
  cursor: pointer;
  & img {
    width: 20px;
  }
}
.flash-icon {
  position: absolute;
  right: 50px;
  width: 25px;
  top: 15px;
}
mat-form-field {
  width: 200px;
  font-size: 18px;
}
.full-screen-icon {
  width: 41px;
  position: relative;
  top: -12px;
  cursor: pointer;
}
.timeline-bar {
  display: flex;
  height: 30px;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.timeline-segment {
  width: 8px; /* fixed width for uniform timeline */
  height: 100%;
}
