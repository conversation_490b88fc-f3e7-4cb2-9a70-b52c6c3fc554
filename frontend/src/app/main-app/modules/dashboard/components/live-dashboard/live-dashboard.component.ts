import { Component, inject, OnInit } from '@angular/core';
import { LiveDashboardService } from './services/live-dashboard.service';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';
import * as _moment from 'moment';

import { default as _rollupMoment } from 'moment';
import { DATE_MONTH_NAME_FORMAT } from '../../../../../app.constant';
import { ShiftManagementService } from '../../../shift-management/services/shift-management.service';
import { MachineStatusComponent } from '../machine-status/machine-status.component';
import { MatDialog } from '@angular/material/dialog';
import { machineStatusMock } from '../mockdata';
import { SettingsService } from '../../../settings/settings.service';
import { AuthService } from '../../../../../core/services/auth.service';

const moment = _rollupMoment || _moment;

export interface DashboardCard {
  signalName: string;
  transmitterName: string;
  transmitterId: string;
  status: 'Online' | 'Offline';
  alerts: number;
  headerColor:
    | 'pink'
    | 'gray'
    | 'purple'
    | 'green'
    | 'lime'
    | 'yellow'
    | 'blue'
    | 'red';
  icon: 'graph-bar' | 'server' | 'layers' | 'bolt' | 'chart-pie';
  graphData: {
    type: 'bar' | 'placeholder';
    values?: { color: 'green' | 'red'; height: number }[];
  };
}

@Component({
  selector: 'cnc-live-dashboard',
  standalone: false,
  templateUrl: './live-dashboard.component.html',
  styleUrl: './live-dashboard.component.scss',
  providers: [provideMomentDateAdapter(DATE_MONTH_NAME_FORMAT)],
})
export class LiveDashboardComponent implements OnInit {
  fullScreenMode = false;
  todateDate: Date = new Date();
  dataSource: any = [];
  selectedDepartment = '';
  selectedTransmitter = 'all';
  selectedSignal = 'all';
  allDepartments: any = [];
  currentUserRole = inject(AuthService).currentUser?.role;
  cardList: any[] = [];
  transmitterList = [
    { id: 'all', name: 'All Transmitters' },
    { id: 'bq500212A', name: 'Main Transmitter' },
    { id: 'bq500213B', name: 'Backup Transmitter' },
  ];

  signalList = [
    { id: 'all', name: 'All Signals' },
    { id: 'Alpha-01', name: 'Alpha-01' },
    { id: 'Beta-02', name: 'Beta-02 (Inactive)' },
    { id: 'Gamma-03', name: 'Gamma-03' },
  ];

  constructor(
    private liveDashboardService: LiveDashboardService,
    private shiftManagementService: ShiftManagementService,
    private dialog: MatDialog,
    private settingsService: SettingsService
  ) {
    this.liveDashboardService.getLiveDashbardData().subscribe((data: any) => {
      this.dataSource = data;
    });

    this.settingsService.fullScreenMode$.subscribe(
      (mode) => (this.fullScreenMode = mode)
    );
  }
  status() {
    const dialogRef = this.dialog.open(MachineStatusComponent, {
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
      data: machineStatusMock,
    });
  }
  ngOnInit(): void {
    this.shiftManagementService
      .getDepartments()
      .subscribe((departments: any) => {
        this.allDepartments = departments.data;
        if (this.allDepartments.length > 0) {
          // this.selectedDepartment = this.allDepartments[0].id;
        }
      });
  }

  onClickFullScreen() {
    this.fullScreenMode = !this.fullScreenMode;
    this.settingsService.setFullScreenMode(this.fullScreenMode);
  }
}
