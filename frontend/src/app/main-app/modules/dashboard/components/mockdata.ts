import { MachineStatusData } from './modal/livedashboard';
export const machineStatusMock: MachineStatusData = {
  transmitter: {
    name: 'Trnsmtr_03',
    id: 'Trnsmttr_ABC_123',
    department: 'Dept. 2',
    operator: 'Name',
    date: 'July 10, 2025',
    shift: 'A',
  },
  statusData: [
    {
      label: 'Run Time',
      value: 200,
      color: 'green',
      icon: '/icons/mdi_clock.svg',
    },
    {
      label: 'Idle Time',
      value: 100,
      color: 'orange',
      icon: '/icons/idealTime.svg',
    },
    {
      label: 'Down Time',
      value: 50,
      color: 'red',
      icon: '/icons/ix_downtime.svg',
    },
  ],
  statusData1: [
    {
      time: '00:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 30 },
        { status: 'Downtime Time', color: 'orange', duration: 15 },
        { status: 'Idle Time', color: 'gray', duration: 15 },
      ],
    },
    {
      time: '02:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 40 },
        { status: 'Breakdown', color: 'red', duration: 20 },
      ],
    },
    {
      time: '04:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 20 },
        { status: 'Downtime Time', color: 'orange', duration: 20 },
        { status: 'Idle Time', color: 'gray', duration: 20 },
      ],
    },
    {
      time: '06:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 30 },
        { status: 'Downtime Time', color: 'orange', duration: 15 },
        { status: 'Idle Time', color: 'gray', duration: 15 },
      ],
    },
    {
      time: '08:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 40 },
        { status: 'Breakdown', color: 'red', duration: 20 },
      ],
    },
    {
      time: '10:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 20 },
        { status: 'Downtime Time', color: 'orange', duration: 20 },
        { status: 'Idle Time', color: 'gray', duration: 20 },
      ],
    },
    {
      time: '12:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 30 },
        { status: 'Downtime Time', color: 'orange', duration: 15 },
        { status: 'Idle Time', color: 'gray', duration: 15 },
      ],
    },
    {
      time: '14:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 40 },
        { status: 'Breakdown', color: 'red', duration: 20 },
      ],
    },
    {
      time: '16:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 20 },
        { status: 'Downtime Time', color: 'orange', duration: 20 },
        { status: 'Idle Time', color: 'gray', duration: 20 },
      ],
    },
    {
      time: '18:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 30 },
        { status: 'Downtime Time', color: 'orange', duration: 15 },
        { status: 'Idle Time', color: 'gray', duration: 15 },
      ],
    },
    {
      time: '20:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 40 },
        { status: 'Breakdown', color: 'red', duration: 20 },
      ],
    },
    {
      time: '22:00',
      blocks: [
        { status: 'Run Time', color: 'green', duration: 20 },
        { status: 'Downtime Time', color: 'orange', duration: 20 },
        { status: 'Idle Time', color: 'gray', duration: 20 },
      ],
    },
  ],
};
