<div class="app-container">
  <div class="header-row">
    <div class="title">
      <label>Department Management</label>
    </div>

    <div class="action-block" *ngIf="currentUserRole !== 'admin'">
      <button
        mat-flat-button
        color="primary"
        class="no-round"
        (click)="onAddDepartment()"
        style="margin-top: 13px"
      >
        <mat-icon>add</mat-icon>
        Add Dept.
      </button>
    </div>
  </div>

  <div class="scroll-wrapper">
    <cnc-loader [show]="loader" />
    <div class="card-container">
      <mat-card class="department-card" *ngFor="let c of departments">
        <!-- Card Top Section -->
        <div class="card-top">
          <!-- Department Logo or Icon -->
          <div
            class="dept-icon"
            [style.background]="c.department_color || '#4097DB'"
          >
            <img
              *ngIf="c.logo_path; else fallbackIcon"
              [src]="c.logo_path"
              class="dept-img"
              loading="lazy"
              alt="{{ c.name }} Logo"
            />
            <ng-template #fallbackIcon>
              <mat-icon class="fallback-icon">inventory_2</mat-icon>
            </ng-template>
          </div>

          <div class="dept-info">
            <div class="dept-header">
              <div class="dept-name" matTooltip="{{ c.name || '' }}">
                {{ c.name }}
              </div>
              <div
                class="shift"
                [ngClass]="{ 'edit-admin': currentUserRole === 'admin' }"
                matTooltip="{{ c.shift || '' }}"
              >
                Shift: {{ c.shift }}

                <!-- Show menu with edit and delete options for non-admins -->
                <ng-container *ngIf="currentUserRole !== 'admin'">
                  <mat-icon [matMenuTriggerFor]="menu" class="menu-icon"
                    >more_vert</mat-icon
                  >
                  <mat-menu #menu="matMenu">
                    <button mat-menu-item (click)="onEditDepartment(c)">
                      Edit Department
                    </button>
                    <button
                      mat-menu-item
                      (click)="deleteDepartment(c.id, c.name)"
                    >
                      Delete Department
                    </button>
                  </mat-menu>
                </ng-container>

                <img
                  *ngIf="currentUserRole === 'admin'"
                  src="icons/edit-pencil.svg"
                  alt="Edit"
                  width="24"
                  height="24"
                  class="menu-icon-edit"
                  (click)="onEditDepartment(c)"
                />
              </div>
            </div>

            <!-- Manager and Users -->
            <div class="manager-info">
              <div class="manager" *ngIf="currentUserRole !== 'admin'">
                <!-- <mat-icon class="icon">person</mat-icon> -->
                <img src="/icons/Group 27.svg" alt="Manager" />

                <div class="label">Manager</div>
                <div class="name">{{ c.managerName }}</div>
              </div>
              <div class="users">
                <img src="/icons/Layer_1.svg" alt="Manager" />
                <div class="label">Total Users</div>
                <div class="value">{{ c.count.users }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card Bottom Stats -->
        <div class="card-bottom">
          <div class="stat">
            <div class="label">Receivers</div>
            <div class="value">{{ c.count.reciever }}</div>
          </div>
          <div class="stat">
            <div class="label">Transmitters</div>
            <div class="value">{{ c.count.transmitter }}</div>
          </div>
          <div class="stat">
            <div class="label">Offline</div>
            <div class="value">{{ c.count.offline }}</div>
          </div>
          <div class="stat">
            <div class="label">Alerts</div>
            <div class="value">{{ c.count.alert }}</div>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
</div>
