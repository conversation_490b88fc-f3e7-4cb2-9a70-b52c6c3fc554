.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-dept-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: var(--mat-sys-primary);
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* Full viewport height */
  overflow: hidden;
}

.title {
  padding: 15px 0px;
  font-size: 20px;
}

.title label {
  font-family: MontserratSemiBold;
}

// .header-row {
//   flex-shrink: 0;
//   padding: 16px;
//   background-color: white; /* optional for visual separation */
//   z-index: 1;
// }

.scroll-wrapper {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-right: 8px;
  padding-bottom: 40px;
  /* Space so last card isn't clipped */
  min-height: 0;
  /* Required for scroll in flexbox */
  position: relative;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
  padding: 16px;
}

.department-card {
  border-radius: 12px;
  padding: 14px;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  width: 32.5%;
  min-width: 400px;
  flex-direction: column;
  background-color: var(--mat-card-background-color);

  .card-top {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .dept-icon {
      background-color: #47b2fa;
      border-radius: 10px;
      width: 70px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      padding: 10px;
      overflow: hidden;
      /* Prevent image overflow */
    }

    .action-block {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
      margin-top: -25px;
    }

    .dept-img {
      max-width: 100%;
      object-fit: contain;
      width: 70px;
      max-height: 70px;
      /* Maintain aspect ratio without cropping */
    }

    .dept-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 70%;

      .dept-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: -1px;

        .dept-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--mat-body-text);
          justify-content: space-between;
          display: flex;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 60%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .shift {
          position: relative;
          font-size: 14px;
          color: var(--mat-body-text);
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 92px;
          padding-right: 15px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;

          &.edit-admin {
            padding-right: 30px;
          }
        }
      }

      .manager-info {
        display: flex;
        justify-content: space-between;

        .manager,
        .users {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 4px 12px;

          .icon {
            color: #2196f3;
          }

          .value {
            font-size: 12px;

            color: var(--mat-body-text);
          }

          .label,
          .name {
            font-size: 12px;
            color: var(--mat-body-text);
          }
        }
      }
    }
  }

  .label {
    font-size: 12px;
    font-weight: bold;
    color: var(--mat-body-text);
  }

  .card-bottom {
    display: flex;
    justify-content: space-between;
    padding-top: 4px;

    .stat {
      text-align: center;

      .value {
        font-size: 14px;
        margin-top: 0.5rem;
        color: var(--mat-body-text);
      }

      .label {
        font-size: 13px;
        font-weight: bold;
        color: var(--mat-body-text);
      }
    }
  }
}

.custom-menu {
  background-color: var(--mat-menu-background-color);
}

.custom-menu-item {
  color: var(--mat-body-text-color);
  font-size: 14px;

  &:hover {
    background-color: var(--mat-menu-item-hover-state-layer-color);
  }
}

.full-width-input {
  border-radius: 8px;
  border: 1px solid var(--Spindle-Gray, #4b4f54);
  background: #fff;
}

.scroll-wrapper {
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
  padding-right: 8px;
  padding-bottom: 100px;
}

/* Optional: Smooth scrolling */
.scroll-wrapper::-webkit-scrollbar {
  width: 6px;
}

.scroll-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.menu-icon {
  position: absolute;
  cursor: pointer;
  opacity: 0.6;
  top: -4px;
  right: -10px;
}
.menu-icon-edit {
  position: absolute;
  cursor: pointer;
  top: -4px;
  right: -2px;
}
