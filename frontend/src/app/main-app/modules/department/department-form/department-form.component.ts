import { Component, Inject } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgxFileDropEntry } from 'ngx-file-drop';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { IDepartmentFormData } from '../modal/department-mgmt.modal';

@Component({
  selector: 'cnc-department-form',
  standalone: false,
  templateUrl: './department-form.component.html',
  styleUrl: './department-form.component.scss',
})
export class DepartmentFormComponent {
  departmentForm: FormGroup;
  file: NgxFileDropEntry | null = null;
  uploadedFile: File | null = null;
  isEdit = false;
  existingImageUrl: string | null = null;

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private dialogRef: MatDialogRef<DepartmentFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.departmentForm = this.fb.group({
      department_name: [
        data.source.name,
        [Validators.required, Validators.minLength(2)],
      ],
      logo: [data.source.logo],
    });
  }

  public dropped(files: NgxFileDropEntry[]): void {
    if (files.length > 0) {
      const droppedFile = files[0];

      if (droppedFile.fileEntry.isFile) {
        this.file = droppedFile;

        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          this.uploadedFile = file;
          this.departmentForm.patchValue({ logo: file });
          this.existingImageUrl = null; // Hide old preview if new file uploaded
        });
      }
    }
  }

  onSubmit(): void {
    if (this.departmentForm.valid) {
      const formDataInput = {
        department_name: this.departmentForm.get('department_name')?.value,
        logo: this.uploadedFile,
      };

      const preparedData = prepareDepartmentRequestObject(formDataInput);

      const formData = new FormData();
      formData.append('department_name', preparedData.department_name || '');

      if (preparedData.logo) {
        formData.append('logo', preparedData.logo);
      }

      if (preparedData.department_color) {
        formData.append('department_color', preparedData.department_color);
      }

      this.dialogRef.close({
        action: this.data.isEdit ? 'update' : 'save',
        data: formData,
      });
    }
  }

  onDelete(): void {
    const deptName =
      this.departmentForm.get('department_name')?.value || 'this';

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Delete Department',
        message: `Are you sure you want to delete the Department <b>${deptName}</b>?`,
        cancelButtonName: 'Cancel',
        okButtonName: 'Ok',
      },
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        this.dialogRef.close({ action: 'delete', data: this.data.source });
      }
    });
  }

  dismiss(): void {
    this.dialogRef.close(null);
  }
}

function prepareDepartmentRequestObject(
  data: any
): Partial<IDepartmentFormData> {
  const hex_colors = {
    Amber: '#F0A30A',
    Brown: '#825A2C',
    Cobalt: '#0050EF',
    Crimson: '#A20025',
    Cyan: '#1BA1E2',
    Magenta: '#D80073',
    Lime: '#A4C400',
    Indigo: '#6A00FF',
    Green: '#60A917',
    Emerald: '#008A00',
    Mauve: '#76608A',
    Olive: '#6D8764',
    Orange: '#FA6800',
    Pink: '#F472D0',
    Red: '#E51400',
    Sienna: '#7A3B3F',
    Steel: '#647687',
    Teal: '#00ABA9',
    Violet: '#AA00FF',
    Yellow: '#D8C100',
  };

  const colorKeys = Object.keys(hex_colors) as (keyof typeof hex_colors)[];
  const randomKey = colorKeys[Math.floor(Math.random() * colorKeys.length)];
  const randomColor = hex_colors[randomKey];

  return {
    department_name: data.department_name,
    logo: data.logo,
    department_color: randomColor,
  };
}
