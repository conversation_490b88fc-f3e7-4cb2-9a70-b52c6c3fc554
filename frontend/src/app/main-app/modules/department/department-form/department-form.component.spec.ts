import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DepartmentFormComponent } from './department-form.component';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'; // Ensure MAT_DIALOG_DATA is imported
import { CoreModule } from '../../../../core/core.module';
import { ReactiveFormsModule } from '@angular/forms';
import { NgxFileDropModule } from 'ngx-file-drop';

describe('DepartmentFormComponent', () => {
  let component: DepartmentFormComponent;
  let fixture: ComponentFixture<DepartmentFormComponent>;

  // Mock MatDialogRef
  const mockMatDialogRef = {
    close: jasmine.createSpy('close'),
  };

  // Mock MAT_DIALOG_DATA (provide an empty object or relevant data if your component expects it)
  const mockDialogData = {
    source: {
      name: 'test',
    },
  }; // <--- This is usually fine, but if component expects specific structure, add it.

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreModule, ReactiveFormsModule, NgxFileDropModule],
      declarations: [DepartmentFormComponent],
      providers: [
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DepartmentFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
