<label class="form-title">{{ data.title }}</label>
<div class="dialog-card">
  <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()" class="form-grid">
    <!-- Department Name -->
    <div class="form-group">
      <label class="form-label"
        >Department Name <span class="required">*</span></label
      >
      <mat-form-field
        appearance="outline"
        class="full-width-field compact-field"
      >
        <input
          matInput
          placeholder="Department name"
          formControlName="department_name"
          required
        />
        <mat-error
          *ngIf="departmentForm.get('department_name')?.hasError('required')"
          class="error"
          >Department name is required.
        </mat-error>
        <mat-error
          *ngIf="departmentForm.get('department_name')?.hasError('minlength')"
          class="error"
        >
          Department name must be at least 2 characters
        </mat-error>
      </mat-form-field>
    </div>
    <!-- Upload -->
    <div class="upload-full">
      <label class="form-label">Upload Image</label>
      <ngx-file-drop (onFileDrop)="dropped($event)">
        <ng-template
          ngx-file-drop-content-tmp
          let-openFileSelector="openFileSelector"
        >
          <div class="upload-box">
            <ng-container *ngIf="!uploadedFile && !existingImageUrl">
              <p>
                Drop your image here or
                <span class="browse-text" (click)="openFileSelector()"
                  >Browse</span
                >
              </p>
            </ng-container>
            <ng-container *ngIf="uploadedFile">
              <p>{{ uploadedFile.name }}</p>
            </ng-container>
          </div>
        </ng-template>
      </ngx-file-drop>
    </div>
  </form>
  <div mat-dialog-actions class="modal-action">
    <!-- <div class="left-actions" style="margin-right: 2rem">
      <button
        *ngIf="data.isEdit"
        mat-stroked-button
        class="action-button danger-button"
        color="warn"
        (click)="onDelete()"
      >
        Delete
      </button>
    </div> -->

    <div class="right-actions">
      <button
        mat-stroked-button
        class="action-button"
        color="primary"
        (click)="dismiss()"
      >
        Cancel
      </button>
      <button
        mat-flat-button
        class="action-button"
        color="primary"
        [disabled]="!departmentForm.valid"
        (click)="onSubmit()"
      >
        {{ data.isEdit ? 'Update' : 'Save' }}
      </button>
    </div>
  </div>
</div>
