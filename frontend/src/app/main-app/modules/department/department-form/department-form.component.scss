.dialog-card {
  display: flex;
  box-sizing: border-box;
  padding: 10px 20px 0px 20px;
  flex-direction: column;
}

.form-title {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  padding: 20px 10px 0px 20px;
  margin-bottom: 0.5rem;
  color: var(--mat-body-text);
}

.full-width-field {
  width: 50% !important;
}

.form-grid {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

mat-form-field mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  margin-left: 10px;
  margin-right: 10px;
}

.upload-full {
  width: 100%;
}

.browse-text {
  color: #1976d2;
  cursor: pointer;
  font-weight: 500;
  margin-left: 5px;
  text-decoration: underline;
}
.modal-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
}

.form-label {
  margin-bottom: 1rem;
  display: block;
  color: var(--mat-body-text);
}

label {
  font-family: MontserratSemiBold;
  font-size: 16px;
  color: var(--mat-body-text);
}
.required {
  color: red;
}
.action-button {
  width: 130px;
  font-weight: 500;
  font-size: 14px;
  height: 40px;
  border-radius: 8px;
  text-transform: none;
}
.danger-button {
  border: 1px solid #ff1b1b;
  color: #ff1b1b;
  background-color: transparent;
}
.error {
  font-size: 12px;
  color: red;
  margin-top: 6px;
}

.modal-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 32px;
  flex-wrap: wrap;
}

.right-actions {
  display: flex;
  gap: 20px;
  align-items: center;
}

.action-button {
  min-width: 120px;
  font-weight: 500;
  font-size: 14px;
  height: 40px;
  border-radius: 8px;
  text-transform: none;
}

.danger-button {
  border: 1px solid #ff1b1b;
  color: #ff1b1b;
  background-color: transparent;
}

.compact-field .mat-form-field {
  height: 36px;
  font-size: 14px;
}

.compact-field .mat-form-field-infix {
  padding: 0;
  min-height: 36px;
}
