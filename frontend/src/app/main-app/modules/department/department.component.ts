import {
  ChangeDetectorRef,
  Component,
  inject,
  Inject,
  OnInit,
} from '@angular/core';
import { DepartmentFormComponent } from './department-form/department-form.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DepartmentService } from './services/department.service';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import {
  IDepartment,
  IDepartmentDialogResult,
  IDepartmentFormData,
  IDepartmentResponse,
} from './modal/department-mgmt.modal';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { imageBasePath } from '../../../app.constant';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'cnc-department',
  standalone: false,
  templateUrl: './department.component.html',
  styleUrl: './department.component.scss',
})
export class DepartmentComponent implements OnInit {
  departments: IDepartment[] = [];
  secondRowItems: string[] = [];
  loader = true;
  duplicate = false;
  backgroundColors: string[] = [];
  currentUserRole = inject(AuthService).currentUser?.role;
  constructor(
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private departmentService: DepartmentService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDepartments();
  }
  onAddDepartment(): void {
    const dialogRef = this.dialog.open(DepartmentFormComponent, {
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
      data: {
        title: 'Add Department',
        isEdit: false,
        source: {
          id: 0,
          name: '',
          logo: '',
        },
      },
    });

    dialogRef.afterClosed().subscribe((result: IDepartmentDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.addDepartment(data);
      }
    });
  }
  addDepartment(data: IDepartmentFormData): void {
    this.loader = true;
    this.departmentService.createDepartment(data).subscribe({
      next: (res: IDepartmentResponse) => {
        this.loadDepartments();
        this.onSucess(res.message);
      },
      error: (error: any) => {
        if (error.status === 409) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 400) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        this.loadDepartments();
      },
    });
  }
  onEditDepartment(dept: IDepartment): void {
    const dialogRef = this.dialog.open(DepartmentFormComponent, {
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
      data: {
        title: 'Edit Department',
        isEdit: true,
        source: {
          id: dept.id,
          name: dept.name,
          logo: dept.logo_path,
        },
      },
    });

    dialogRef.afterClosed().subscribe((result: IDepartmentDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'update') {
        this.loader = true;
        this.updateDepartment(dept.id, data);
      } else if (action === 'delete') {
        this.loader = true;
        this.deleteDepartment(dept.id, dept.name);
      }
    });
  }
  updateDepartment(deptId: string, dept: Partial<IDepartment>): void {
    this.departmentService.updateDepartment(deptId, dept).subscribe({
      next: (res: IDepartmentResponse) => {
        this.loadDepartments();
        this.onSucess('Department updated successfully!');
      },
      error: (error: any) => {
        if (error.status === 409) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 500) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 400) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        this.loadDepartments();
        this.loader = false;
      },
    });
  }

  deleteDepartment(deptId: string, deptName: string): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Delete Department',
        message: `Are you sure you want to delete the Department  <b>${deptName}</b> ?`,
        cancelButtonName: 'Cancel',
        okButtonName: 'Delete',
      },
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        this.loader = true;
        this.departmentService.deleteDepartment(deptId).subscribe({
          next: () => {
            this.loadDepartments();
            this.onSucess('Department deleted successfully!');
            this.loader = false;
          },
          error: (error: any) => {
            this.snackbarService.open(error.error.message, '', 'failed', 3000);
            this.loader = false;
          },
        });
      }
    });
  }

  loadDepartments(): void {
    this.departmentService.getDepartments().subscribe({
      next: (res: IDepartmentResponse) => {
        this.departments = res.data.map((dept: IDepartment) => ({
          ...dept,
          logo_path: dept.logo_path ? imageBasePath + dept.logo_path : null,
          department_color: dept.department_color || '#4097DB',
        }));
        this.cdr.detectChanges();
        this.loader = false;
      },
      error: (err) => {
        console.error('Failed to load departments', err);
        this.loader = false;
        if (err.status === 409) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
        if (err.status === 500) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
        if (err.status === 400) {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        }
      },
    });
  }
  onSucess(message: string) {
    this.snackbarService.open(message, '', 'success', 3000);
  }

  onError(message: string, error: any) {
    this.loader = false;

    console.error(message, error);
  }
}
