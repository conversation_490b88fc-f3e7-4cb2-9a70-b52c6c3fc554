import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DepartmentComponent } from './department.component';
import { DepartmentRoutingModule } from './department.routing.module';
import { MainAppModule } from '../../main-app.module';
import { MatCardModule } from '@angular/material/card';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { DepartmentFormComponent } from './department-form/department-form.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { NgxFileDropModule } from 'ngx-file-drop';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { SharedModule } from '../../../shared/shared.module';
import { CoreModule } from '../../../core/core.module';
import { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';
@NgModule({
  declarations: [DepartmentComponent, DepartmentFormComponent],
  imports: [
    CommonModule,
    SharedModule,
    CoreModule,
    DepartmentRoutingModule,
    MatSelectModule,
    MainAppModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule,
    MatMenuModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatIcon,
    NgxFileDropModule,
    MatInputModule,
    MatTooltipModule,
  ],
})
export class DepartmentModule {}
