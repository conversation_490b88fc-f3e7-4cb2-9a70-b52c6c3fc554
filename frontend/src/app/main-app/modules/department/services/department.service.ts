import { Injectable } from '@angular/core';
import { departmenturl } from '../../../../app.constant';

import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { IDepartmentResponse } from '../modal/department-mgmt.modal';
@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  constructor(private http: HttpClient) {}
  getDepartments(): Observable<IDepartmentResponse> {
    return this.http.get<IDepartmentResponse>(departmenturl + 'list');
  }
  createDepartment(data: any): Observable<IDepartmentResponse> {
    return this.http.post<IDepartmentResponse>(departmenturl + 'create', data);
  }
  updateDepartment(id: string, data: any): Observable<IDepartmentResponse> {
    return this.http.put<IDepartmentResponse>(
      departmenturl + 'update/' + id,
      data
    );
  }

  deleteDepartment(id: string): Observable<IDepartmentResponse> {
    return this.http.delete<IDepartmentResponse>(
      departmenturl + 'delete/' + id
    );
  }
}
