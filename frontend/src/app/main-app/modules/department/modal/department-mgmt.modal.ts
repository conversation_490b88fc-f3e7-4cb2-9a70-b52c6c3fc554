export interface IDepartmentResponse {
  status: boolean;
  data: IDepartment[];
  error_code: string | null;
  errors: any[];
  message: string;
}

export interface IDepartment {
  id: string;
  name: string;
  logo_path: string | null;
  mimetype: string | null;
  created_at: string;
  updated_at: string;
  shift: string;
  managerName: string;
  department_color: string;
  count: {
    reciever: number;
    transmitter: number;
    users: number;
    alert: number;
    offline: number;
  };
}

export interface IDepartmentDialogData {
  title: string;
  isEdit: boolean;
  source: IDepartmentFormData;
  maxWidth?: string;
  width?: string;
  panelClass?: string;
}

export interface IDepartmentDialogResult {
  action: 'save' | 'update' | 'delete';
  data: IDepartmentFormData;
}

export interface IDepartmentFormData {
  id: string;
  department_name: string;
  logo: File | string | null;
  department_color: string;
}
