<div class="profile-container" [formGroup]="profileForm">
  <!-- Header -->
  <div class="header">
    <h3>View Profile</h3>
    <button
      mat-flat-button
      color="primary"
      class="edit-btn no-round"
      (click)="onEditProfile()"
    >
      <mat-icon>edit</mat-icon>
      Edit
    </button>
  </div>

  <!-- 2-column Grid -->
  <div class="form-grid">
    <!-- Row 1 -->
    <div class="form-field">
      <label for="firstname">First Name</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="firstname"
          placeholder="First Name"
          formControlName="firstname"
          [readonly]="true"
          class="readonly"
        />
      </mat-form-field>
    </div>

    <div class="form-field">
      <label for="lastname">Last Name</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="lastname"
          placeholder="Last Name"
          formControlName="lastname"
          [readonly]="true"
        />
      </mat-form-field>
    </div>

    <!-- Row 2 -->
    <div class="form-field phone-row full-width">
      <label>Phone Number</label>
      <div class="combined-phone-input">
        <!-- Country Code -->
        <!-- <mat-form-field appearance="outline" class="code-select disabled-input">
          <mat-select formControlName="countryCode" required>
            <mat-option value="+91">+91</mat-option>
            <mat-option value="+1">+1</mat-option>
            <mat-option value="+44">+44</mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- Phone Number -->
        <mat-form-field appearance="outline" class="phone-input disabled-input">
          <input
            matInput
            type="tel"
            placeholder="Phone Number"
            formControlName="phone"
            required
            [readonly]="true"
          />
        </mat-form-field>
      </div>
    </div>

    <div class="form-field">
      <label for="email">Email</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
        readonly
      >
        <input
          matInput
          id="email"
          placeholder="Email"
          formControlName="email"
          [readonly]="true"
        />
      </mat-form-field>
    </div>

    <!-- Row 3 -->
    <div class="form-field">
      <label for="username">Username</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="username"
          placeholder="Username"
          formControlName="username"
          [readonly]="true"
        />
      </mat-form-field>
    </div>
    <div class="form-field">
      <label for="username">UserRole</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="userrole"
          placeholder="UserRole"
          formControlName="userrole"
          [readonly]="true"
        />
      </mat-form-field>
    </div>
    <div class="form-field" *ngIf="currentUserRole !== 'superadmin'">
      <label for="Department">Department</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="userrole"
          placeholder="Department"
          formControlName="departments"
          [readonly]="true"
        />
      </mat-form-field>
    </div>
    <div class="form-field" *ngIf="currentUserRole !== 'superadmin'">
      <label for="Shifts">Shift</label>
      <mat-form-field
        appearance="outline"
        class="full-width-field disabled-input"
      >
        <input
          matInput
          id="shifts"
          placeholder="Shift"
          formControlName="shifts"
          [readonly]="true"
        />
      </mat-form-field>
    </div>

    <div class="form-field empty-column"></div>
  </div>
</div>
