import { Component, inject, Inject } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { STRONGPASSWORDPATTERN } from '../../../../app.constant';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { AddEditUserComponent } from '../../user-management/add-edit-user/add-edit-user.component';
import { IUserDepartment, IUser } from '../../user-management/modal/user.modal';
import { ProfileService } from '../profile.service';
import { SnackbarService } from '../../../../shared/services/snackbar.service';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'cnc-edit-profile',
  standalone: false,
  templateUrl: './edit-profile.component.html',
  styleUrl: './edit-profile.component.scss',
})
export class EditProfileComponent {
  userForm: FormGroup;
  allDepartments: IUserDepartment[] = [];
  shiftList: { id: string; name: string }[] = [];
  hidePassword = true;
  currentUserRole = inject(AuthService).currentUser?.role;
  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private profileService: ProfileService,
    private snackbarService: SnackbarService,
    public dialogRef: MatDialogRef<AddEditUserComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.userForm = this.fb.group({
      firstName: [data.source.firstName, Validators.required],
      lastName: [data.source.lastName, Validators.required],
      phoneNo: [
        data.source.phone,
        [Validators.required, Validators.pattern(/^\d{10,}$/)],
      ],
    });
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      const userData = prepareRequestObject(this.userForm.value);

      if (this.data) {
        this.profileService.updateProfileData(userData).subscribe({
          next: (response) => {
            this.dialogRef.close({
              action: 'update',
              data: response,
            });
            this.snackbarService.open(response.message, '', 'success', 3000);
          },

          error: (error) => {
            console.error('Update failed', error);
          },
        });
      } else {
        this.dialogRef.close({
          action: 'save',
          data: userData,
        });
      }
    }
  }

  onDelete(): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Confirmation',
        message: 'Are you sure you want to delete this user?',
        cancelButtonName: 'Cancel',
        okButtonName: 'Ok',
      },
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        this.dialogRef.close({ action: 'delete', data: this.data.source });
      }
    });
  }

  dismiss(): void {
    this.dialogRef.close(null);
  }
}

function prepareRequestObject(data: any): Partial<IUser> {
  return {
    firstname: data.firstName,
    lastname: data.lastName,
    phone: data.phoneNo,
    email: data.email,
    username: data.userName,
  };
}
