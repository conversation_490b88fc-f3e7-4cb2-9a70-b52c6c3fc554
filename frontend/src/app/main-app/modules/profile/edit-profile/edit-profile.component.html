<label>
  {{ data.title }}
</label>

<div class="modal-content">
  <!-- Dialog Content -->
  <div mat-dialog-content class="custom-dialog-content">
    <form [formGroup]="userForm" class="form-grid" (ngSubmit)="onSubmit()">
      <div class="user-control-ele">
        <label class="form-label"
          >First Name <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>person</mat-icon>
          </span>
          <input
            matInput
            formControlName="firstName"
            placeholder="First Name"
          />
          <mat-error *ngIf="userForm.get('firstName')?.hasError('required')">
            First Name is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Last Name <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>person</mat-icon>
          </span>
          <input matInput formControlName="lastName" placeholder="Last Name" />
          <mat-error *ngIf="userForm.get('lastName')?.hasError('required')">
            Last Name is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="user-control-ele">
        <label class="form-label"
          >Phone No. <span class="required">*</span></label
        >
        <mat-form-field appearance="outline" class="full-width-field">
          <span matPrefix class="prefix-icon">
            <mat-icon>phone</mat-icon>
          </span>
          <input matInput formControlName="phoneNo" placeholder="Phone No." />
          <mat-error *ngIf="userForm.get('phoneNo')?.hasError('required')">
            Phone Number is required
          </mat-error>
          <mat-error *ngIf="userForm.get('phoneNo')?.hasError('pattern')">
            Invalid phone number
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </div>
</div>
<div
  mat-dialog-actions
  class="modal-action actiondiv"
  [ngClass]="{ 'edit-mode': data.isEdit }"
>
  <button
    *ngIf="!data.isEdit"
    mat-stroked-button
    class="action-button danger-button"
    color="warn"
    (click)="onDelete()"
  >
    Delete
  </button>
  <div class="ri-btns">
    <button
      mat-stroked-button
      class="action-button"
      color="primary"
      (click)="dismiss()"
    >
      Cancel
    </button>
    <button
      mat-flat-button
      class="action-button"
      color="primary"
      tabindex="-1"
      [disabled]="!userForm.valid"
      (click)="onSubmit()"
    >
      {{ data.isEdit ? 'Update' : 'Save' }}
    </button>
  </div>
</div>
