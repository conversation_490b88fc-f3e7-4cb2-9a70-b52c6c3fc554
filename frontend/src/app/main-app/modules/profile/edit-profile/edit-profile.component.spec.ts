import { ComponentFixture, TestBed } from '@angular/core/testing';

import { EditProfileComponent } from './edit-profile.component';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { first } from 'rxjs';
import { CoreModule } from '../../../../core/core.module';
import { ReactiveFormsModule } from '@angular/forms';

// Mock MatDialogRef
const mockMatDialogRef = {
  close: jasmine.createSpy('close'),
};

// Mock MAT_DIALOG_DATA (provide an empty object or relevant data if your component expects it)
const mockDialogData = {
  source: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '**********',
    username: 'johndo<PERSON>',
  },
}; // <--- This is usually fine, but if component expects specific structure, add it.

describe('EditProfileComponent', () => {
  let component: EditProfileComponent;
  let fixture: ComponentFixture<EditProfileComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreModule, ReactiveFormsModule],
      declarations: [EditProfileComponent],
      providers: [
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditProfileComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
