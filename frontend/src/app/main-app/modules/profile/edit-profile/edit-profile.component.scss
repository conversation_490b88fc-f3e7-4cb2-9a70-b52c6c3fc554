.modal-action {
  width: 97%;
}

label {
  padding: 20px;
  font-family: MontserratSemiBold;
  font-size: 20px;
  padding-bottom: 5px;
}
.dialog-form {
  margin-top: -20px;
}

hr {
  border: 1px solid red;
  border-color: var(--mat-sys-primary-container);
}
p {
  margin-top: 12px;
  margin-right: 36px;
}

button {
  border-radius: 6px;
}

.txt-center {
  text-align: center;
  line-height: 22px;
  margin: 0px;
}

.form-label {
  display: block;
  font-size: 14px;
  padding: 0px;
  margin-bottom: 5px;
}

.required {
  color: #ff1b1b;
}
.error-message {
  color: #ff1b1b;
  font-size: 12px;
}
.prefix-icon mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  margin-left: 10px;
  margin-right: 10px;
}
.time-selection-container {
  margin-top: 20px;
}
.time-input.first {
  margin-right: 30px;
}

.action-button {
  width: 150px;
}
.action-button:first-child {
  margin-right: 20px;
}
.danger-button {
  border: 1px solid #ff1b1b;
  color: #ff1b1b;
}
mat-error {
  margin-left: -16px;
}

/* CSS Grid for the two-column layout */
.form-grid {
  display: grid;
  grid-template-columns: 1fr; /* Single column by default for mobile */
  gap: 12px 35px;
}

@media (min-width: 768px) {
  /* Apply two columns on medium screens and up */
  .form-grid {
    grid-template-columns: 1fr 1fr; /* Two equal columns */
  }
}

/* Ensure form fields take full width within their grid cell */
mat-form-field {
  width: 100%;
}

/* Specific styling for the phone code select within mat-form-field */
.phone-code-select {
  width: 25%; /* Adjust as needed */
  margin-right: 8px; /* Space between select and input */
}

/* Styling for the department chips container */
.department-chips {
  grid-column: 1 / -1; /* Span across all columns */
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Space between chips */
  margin-top: -16px; /* Adjust to align with form fields */
  margin-bottom: 16px;
}

/* Styling for form actions (buttons) */
.form-actions {
  grid-column: 1 / -1; /* Span across all columns */
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  gap: 16px; /* Space between buttons */
  margin-top: 24px; /* Space above buttons */
}

.form-actions button {
  padding: 8px 24px;
  border-radius: 4px;
  font-weight: 500;
}
.required {
  color: #ff1b1b;
}
.custom-dialog-content {
  padding: 20px;
}
.actiondiv {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 10px;
  padding-right: 10px;
}
