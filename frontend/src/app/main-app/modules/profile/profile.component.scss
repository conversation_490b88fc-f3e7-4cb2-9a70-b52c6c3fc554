.profile-container {
  border-radius: 6px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-button {
  min-width: 120px;
  font-weight: 500;
  font-size: 14px;
  height: 40px;
  border-radius: 8px;
  text-transform: none;
}
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 2 equal columns */
  gap: 16px;
  margin-top: 16px;
}

.form-field {
  width: 100%;
}

.form-grid {
  width: 60%;
  min-width: 300px; /* Optional */
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}
.full-width-field {
  width: 100%;
}

.empty-column {
  /* keeps the grid structure; can be empty */
  min-height: 56px; /* same height as mat-form-field */
}

.combined-phone-input {
  display: flex;
  width: 100%;
  gap: 0;
}

.code-select {
  flex: 0 0 100px;
  margin-right: -1px; /* Join fields visually */
}

.phone-input {
  flex: 1;
}
label {
  font-family: MontserratSemiBold;
  font-size: 16px;
}

mat-form-field {
  margin-top: 10px;
}
.deptchip-name {
  padding: 10px;
  border-radius: 8px;
  background-color: var(--gray-bg);
  border: 1px solid;
  width: 100%;
  min-height: 20px;
}
.deptchip-name-block {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}
