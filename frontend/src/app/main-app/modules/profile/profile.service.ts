import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { userUrl } from '../../../app.constant';
import { Observable } from 'rxjs';
import { GetProfileResponse } from './profile.modal';

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  constructor(private http: HttpClient) {}

  getProfileData(): Observable<GetProfileResponse> {
    return this.http.get<GetProfileResponse>(`${userUrl}profile`);
  }
  updateProfileData(data: any): Observable<any> {
    return this.http.put(`${userUrl}update`, data);
  }
}
