import { Component, inject, OnInit } from '@angular/core';
import { EditProfileComponent } from './edit-profile/edit-profile.component';
import { MatDialog } from '@angular/material/dialog';
import {
  IUserDepartment,
  IUserDialogResult,
} from '../user-management/modal/user.modal';
import { UserManagementService } from '../user-management/services/user-management.service';
import { ProfileService } from './profile.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { forkJoin } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'cnc-profile',
  standalone: false,
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent implements OnInit {
  departmentList: IUserDepartment[] = [];
  shiftList: { id: string; name: string }[] = [];
  profileForm: FormGroup;
  displayedDepartmentName = '';
  displayedShiftName = '';
  currentUserRole = inject(AuthService).currentUser?.role;
  constructor(
    private dialog: MatDialog,
    private profile: ProfileService,
    private fb: FormBuilder
  ) {
    this.profileForm = this.fb.group({
      firstname: [''],
      lastname: [''],
      phone: [''],
      countryCode: ['+91'],
      email: [''],
      username: ['', { disabled: true }],
      userrole: [''],
      departments: [[]],
      shifts: [[]],
    });
  }
  ngOnInit() {
    this.getProfileData();
  }

  getProfileData() {
    this.profile.getProfileData().subscribe({
      next: (res: any) => {
        const data = res?.data?.[0] || {};
        this.profileForm.patchValue({
          firstname: data.firstname,
          lastname: data.lastname,
          phone: data.phone,
          email: data.email,
          username: data.username,
          userrole: data.role,
          departments: data.departments,
          shifts: data.shifts,
        });
        // if (data.departments && Array.isArray(data.departments)) {
        //   this.displayedDepartmentName = this.getDepartmentName(
        //     data.departments
        //   );
        // } else {
        //   this.displayedDepartmentName = '';
        // }

        // if (data.shifts && Array.isArray(data.shifts)) {
        //   this.displayedShiftName = this.getShiftName(data.shifts);
        // } else {
        //   this.displayedShiftName = '';
        // }
      },
    });
  }
  onEditProfile() {
    const phone = this.profileForm.get('phone')?.value || '';
    const dialogRef = this.dialog.open(EditProfileComponent, {
      data: {
        title: 'Edit Profile',
        isEdit: true,
        source: {
          id: 0,
          firstName: this.profileForm.get('firstname')?.value,
          lastName: this.profileForm.get('lastname')?.value,
          phone: `${this.profileForm.get('phone')?.value}`,
          email: this.profileForm.get('email')?.value,
          username: this.profileForm.get('username')?.value,
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((result: IUserDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.profileForm.patchValue({
          firstname: data.firstname,
          lastname: data.lastname,
          phone: data.phone,
          countryCode: data.phone,
          email: data.email,
          username: data.username,
        });
      }
      this.getProfileData();
    });
  }
}
