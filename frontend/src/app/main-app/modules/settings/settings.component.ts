import {
  Component,
  OnInit,
  ChangeDetectorRef,
  Renderer2,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgxFileDropEntry } from 'ngx-file-drop';
import { FileSystemFileEntry } from 'ngx-file-drop';
import { SettingsService } from './settings.service';
import { ISettingsResponse } from './model/settings.model';
import { imageBasePath, THEME_COLORS } from '../../../app.constant';
import { SnackbarService } from '../../../shared/services/snackbar.service';

@Component({
  selector: 'cnc-settings',
  templateUrl: './settings.component.html',
  standalone: false,
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit {
  settingsForm: FormGroup;
  file: NgxFileDropEntry | null = null;
  uploadedFile: File | null = null;
  isEdit = false;
  showPreview = false;
  existingImageUrl: string | null = null;
  currentSettingsId: string | null = null;
  colors = THEME_COLORS;
  loader = true;
  selectedColor: { id: string; value: string } | null = null;
  @ViewChild('logoInput') logoInputRef!: ElementRef<HTMLInputElement>;
  constructor(
    private fb: FormBuilder,
    private settingsService: SettingsService,
    private cdr: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private renderer: Renderer2
  ) {
    this.settingsForm = this.fb.group({
      company_name: [''],
      logo: [null],
      theme: [''],
    });
  }

  ngOnInit(): void {
    this.loadSettings();
  }

  loadSettings(): void {
    this.settingsService.getSettings().subscribe({
      next: (res: ISettingsResponse) => {
        console.log('API response:', res);

        if (res.status && Array.isArray(res.data) && res.data.length > 0) {
          const setting = res.data[0];

          this.isEdit = true;
          this.currentSettingsId = setting.id;
          console.log('Current Settings ID:', this.currentSettingsId);

          this.settingsForm.patchValue({
            company_name: setting.company_name || '',
            theme: setting.theme || '',
            logo: null,
          });

          this.selectedColor =
            this.colors.find((c) => c.id === setting.theme) || null;
        }
      },
      error: (err) => {
        console.error('Failed to load settings:', err);
      },
    });
  }

  public dropped(files: NgxFileDropEntry[]): void {
    if (files.length > 0) {
      const droppedFile = files[0];
      if (droppedFile.fileEntry.isFile) {
        this.file = droppedFile;

        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          this.uploadedFile = file;
          this.settingsForm.patchValue({ logo: file });
          this.existingImageUrl = null;
        });
      }
    }
  }

  selectColor(color: { id: string; value: string }) {
    this.selectedColor = color;
    this.settingsForm.patchValue({ theme: color.id }); // Only send 'blue-theme' etc.
  }
  get themeImageUrl(): string | null {
    return this.selectedColor ? `/images/${this.selectedColor.id}.png` : null;
  }

  onSubmit() {
    if (this.settingsForm.valid) {
      this.loader = true;
      const formValue = this.settingsForm.value;
      const formData = new FormData();

      formData.append('company_name', formValue.company_name);
      formData.append('theme', formValue.theme);

      if (formValue.logo) {
        formData.append('logo', formValue.logo);
      }

      const request = this.isEdit
        ? this.settingsService.updateSettings(this.currentSettingsId!, formData)
        : this.settingsService.createSettings(formData);

      request.subscribe({
        next: (res) => {
          if (res.status && res.data?.length > 0) {
            const settings = res.data[0];
            this.currentSettingsId = settings.id;
            this.loader = false;
            this.isEdit = true;
            this.snackbarService.open(res.message, '', 'success', 3000);

            const logoUrl = `${imageBasePath + settings.logo_path}?v=${new Date().getTime()}`;
            this.settingsService.logoImg.set(logoUrl);
            this.settingsService.systemSettings = settings;

            this.uploadedFile = null;
            this.file = null;
            this.settingsForm.patchValue({ logo: null });
            this.existingImageUrl = null;

            const theme = settings.theme?.trim() || 'blue-theme';
            const body = document.body;
            const currentTheme = Array.from(body.classList).find((cls) =>
              cls.endsWith('-theme')
            );
            if (currentTheme) this.renderer.removeClass(body, currentTheme);
            this.renderer.addClass(body, theme);
          }
        },

        error: (err) => {
          console.error('Failed to save settings:', err);
        },
      });
    } else {
      this.settingsForm.markAllAsTouched();
    }
  }
}
