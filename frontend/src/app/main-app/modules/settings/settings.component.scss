.settings-form {
  .required {
    color: red;
  }
  .drop-zone {
    border: 2px dashed #ccc;
    padding: 1rem;
    text-align: center;
    margin: 0.5rem 0;
    border-radius: 8px;
    width: 50%;
  }

  .upload-logo {
    width: 70%;
  }

  .file-info {
    font-size: 12px;
    color: #666;
  }

  .color-picker {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;

    .color-box {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      border: 2px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;

      &.selected {
        font-weight: bold;
        color: white;
      }
    }
  }

  button {
    margin-top: 1rem;
  }
}

.title {
  padding: 15px 0px;
  font-size: 20px;
  margin-bottom: 1rem;
}

.title label {
  font-family: MontserratSemiBold;
}

.settings-container {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.form-footer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 10;
}

.action-button {
  min-width: 120px;
  font-weight: 500;
  font-size: 14px;
  height: 40px;
  border-radius: 8px;
  text-transform: none;
}

.form-label {
  margin-bottom: 1rem;
  display: block;
}

label {
  font-family: MontserratSemiBold;
  font-style: normal;
  font-weight: 500;
}
.settings-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Optional widths */
.settingbox {
  flex: 1;
  min-width: 300px;
}

.preview {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 10px;
}
.preview-section {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.preview-section mat-checkbox {
  margin-bottom: 5px;
  display: block;
}

.preview {
  padding: 20px;

  border-radius: 10px;
  border: 1px solid #eee;
}

.preview img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}
.browse-text {
  color: #1976d2;
  cursor: pointer;
  font-weight: 500;
  margin-left: 5px;
  text-decoration: underline;
}
