export interface ISettingsPayload {
  company_name: string;
  logo: File | string | null;
  theme: string;
}

export interface ISettingsResponse {
  status: boolean;
  data: ISettings[]; // ✅ Correct - API returns an array
  error_code: string | null;
  errors: any[];
  message: string;
}

export interface ISettings {
  id: string;
  company_name: string;
  logo_path: string | null;
  theme: string;
  mimetype: string | null; // ✅ Added from your API response
  created_at: string;
  updated_at: string;
}
