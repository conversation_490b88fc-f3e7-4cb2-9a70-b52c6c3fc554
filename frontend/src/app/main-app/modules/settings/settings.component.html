<div class="title">
  <label>Settings</label>
</div>

<div class="settings-container">
  <div class="settingbox">
    <form class="settings-form" [formGroup]="settingsForm">
      <label class="form-label">Company Name </label>
      <mat-form-field appearance="outline" class="upload-logo">
        <input
          matInput
          placeholder="Company Name"
          formControlName="company_name"
        />
        <mat-error
          *ngIf="
            settingsForm.get('company_name')?.invalid &&
            settingsForm.get('company_name')?.touched
          "
        >
          Company name is required.
        </mat-error>
      </mat-form-field>

      <div class="upload-logo">
        <label class="form-label">Upload company logo</label>
        <ngx-file-drop (onFileDrop)="dropped($event)">
          <ng-template
            ngx-file-drop-content-tmp
            let-openFileSelector="openFileSelector"
          >
            <div class="upload-box">
              <ng-container *ngIf="!uploadedFile && !existingImageUrl">
                <p>
                  Drop your image here or
                  <span class="browse-text" (click)="openFileSelector()"
                    >Browse</span
                  >
                </p>
              </ng-container>
              <ng-container *ngIf="uploadedFile">
                <p>{{ uploadedFile.name }}</p>
              </ng-container>
            </div>
          </ng-template>
        </ngx-file-drop>
        <p class="file-info">
          File format: SVG or .PNG | Max File Size: 200 kb
        </p>
      </div>

      <label>Select Accent Color</label>
      <div class="color-picker">
        <div
          *ngFor="let color of colors"
          class="color-box"
          [ngStyle]="{ 'background-color': color.value }"
          [class.selected]="selectedColor?.id === color.id"
          (click)="selectColor(color)"
        >
          <span *ngIf="selectedColor?.id === color.id">✔</span>
        </div>
      </div>
    </form>
  </div>

  <!-- <div class="preview-section">
    <mat-checkbox [(ngModel)]="showPreview">Preview</mat-checkbox>
    <div class="preview" *ngIf="showPreview">
      <img *ngIf="existingImageUrl" [src]="existingImageUrl" alt="Preview" />
      <img
        *ngIf="!existingImageUrl"
        src="images/preview.jpg"
        alt="Default Preview"
      />
    </div>
  </div> -->
  <div class="preview-section">
    <div class="preview">
      <img
        *ngIf="themeImageUrl"
        [src]="themeImageUrl"
        [alt]="selectedColor?.id"
      />
    </div>
  </div>
</div>

<div class="form-footer">
  <button
    mat-flat-button
    class="action-button"
    color="primary"
    type="submit"
    (click)="onSubmit()"
  >
    Save
  </button>
</div>
