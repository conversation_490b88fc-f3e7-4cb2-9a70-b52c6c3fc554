import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ISettingsResponse, ISettingsPayload } from './model/settings.model';
import { HttpClient } from '@angular/common/http';
import { settingsUrl } from '../../../app.constant';

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  systemSettings: any;
  public logoImg = signal<string | null>(null);

  fullScreenMode$ = new BehaviorSubject<boolean>(false);

  get fullScreenMode() {
    return this.fullScreenMode$.asObservable;
  }

  setFullScreenMode(mode: boolean) {
    this.fullScreenMode$.next(mode);
  }

  constructor(private http: HttpClient) {}

  getSettings(): Observable<ISettingsResponse> {
    return this.http.get<ISettingsResponse>(settingsUrl + 'list');
  }

  createSettings(data: FormData): Observable<ISettingsResponse> {
    return this.http.post<ISettingsResponse>(settingsUrl + 'create', data);
  }

  updateSettings(id: string, data: FormData): Observable<ISettingsResponse> {
    return this.http.put<ISettingsResponse>(settingsUrl + 'update/' + id, data);
  }
}
