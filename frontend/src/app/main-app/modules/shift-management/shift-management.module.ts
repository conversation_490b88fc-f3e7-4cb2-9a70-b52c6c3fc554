import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ShiftManagementRoutingModule } from './shift-management-routing.module';
import { CoreModule } from '../../../core/core.module';
import { ShiftManagementComponent } from './shift-management.component';
import { AddEditShiftComponent } from './add-edit-shift/add-edit-shift.component';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTimepickerModule } from '@angular/material/timepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { SharedModule } from '../../../shared/shared.module';
import { MatTooltipModule } from '@angular/material/tooltip';

@NgModule({
  declarations: [ShiftManagementComponent, AddEditShiftComponent],
  imports: [
    CommonModule,
    ShiftManagementRoutingModule,
    CoreModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatTimepickerModule,
    MatTooltipModule,
  ],
  providers: [provideNativeDateAdapter()],
})
export class ShiftManagementModule {}
