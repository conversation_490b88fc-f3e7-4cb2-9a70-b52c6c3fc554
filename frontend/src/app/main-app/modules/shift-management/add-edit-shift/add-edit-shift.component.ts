import { Component, inject, Inject } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { DateAdapter } from '@angular/material/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import {
  getTimeOnly,
  parseTimeToToday,
} from '../../../../core/helper/functions.utility';
import { IShift, IShiftDepartment } from '../modal/shift-mgmt.modal';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'cnc-add-edit-shift',
  standalone: false,
  templateUrl: './add-edit-shift.component.html',
  styleUrl: './add-edit-shift.component.scss',
})
export class AddEditShiftComponent {
  shiftForm: FormGroup;
  allDepartments: IShiftDepartment[] = [];

  // Injecting DateAdapter to handle date formatting based on 24hr or 12hr format
  private readonly _adapter =
    inject<DateAdapter<unknown, unknown>>(DateAdapter);

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<AddEditShiftComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // Initialize the date adapter locale based on the 24hr or 12hr format
    this._adapter.setLocale(data.source.format24hr ? 'de-DE' : 'en-US');

    const breaksArray = data.source.breaks?.map((bi: any) =>
      this.fb.group({
        breakStartTime: [parseTimeToToday(bi.breakStartTime)],
        breakEndTime: [parseTimeToToday(bi.breakEndTime)],
      })
    );
    this.shiftForm = this.fb.group({
      shiftName: [
        data.source.name,
        [Validators.required, Validators.minLength(2)],
      ],
      is24hr: [data.source.format24hr],
      startTime: [parseTimeToToday(data.source.startTime), Validators.required],
      endTime: [parseTimeToToday(data.source.endTime), Validators.required],
      breaks: this.fb.array(breaksArray || [this.newBreak()]),
      departments: [data.source.departments, Validators.required],
    });

    this.allDepartments = data.departmentList || [];
  }

  // Getter for easy access to the breaks FormArray
  get breaks(): FormArray {
    return this.shiftForm.get('breaks') as FormArray;
  }

  // Getter for easy access to the departments FormControl
  get departments(): FormControl {
    return this.shiftForm.get('departments') as FormControl;
  }

  // Getter for the list of selected departments
  get selectedDepartments(): string[] {
    return this.departments.value || [];
  }

  getDpName(dpId: string): string {
    return (
      this.allDepartments.find((dp: IShiftDepartment) => dp.id === dpId)
        ?.name || ''
    );
  }

  getValidTime(startTime: any, endTime: any) {
    return parseTimeToToday(startTime).getTime() >
      parseTimeToToday(endTime).getTime()
      ? startTime
      : endTime;
  }

  onFormatChange(args: MatSlideToggleChange): void {
    this._adapter.setLocale(args.checked ? 'de-DE' : 'en-US');
  }

  // Creates a new FormGroup for a break time
  newBreak(): FormGroup {
    return this.fb.group({
      breakStartTime: [''],
      breakEndTime: [''],
    });
  }

  // Adds a new break time FormGroup to the FormArray
  addBreak(): void {
    this.breaks.push(this.newBreak());
  }

  // Removes a break time FormGroup from the FormArray at a given index
  removeBreak(index: number): void {
    this.breaks.removeAt(index);
  }

  // Removes a department from the form control value
  removeDepartment(department: string): void {
    const currentDepartments = this.departments.value as string[];
    const index = currentDepartments.indexOf(department);

    if (index >= 0) {
      currentDepartments.splice(index, 1);
      this.departments.setValue([...currentDepartments]);
    }
  }

  onSubmit(): void {
    if (this.shiftForm.valid) {
      const shiftData = prepareRequestObject(this.shiftForm.value);
      this.dialogRef.close({
        action: this.data.isEdit ? 'update' : 'save',
        data: shiftData,
      });
    }
  }

  onDelete(): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Confirmation',
        message: 'Are you sure you want to delete this shift?',
        cancelButtonName: 'Cancel',
        okButtonName: 'Ok',
      },
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        this.dialogRef.close({ action: 'delete', data: this.data.source });
      }
    });
  }

  dismiss(): void {
    this.dialogRef.close(null);
  }
}

function prepareRequestObject(data: any): Partial<IShift> {
  return {
    shift_name: data.shiftName,
    time_format: data.is24hr ? '24hr' : '12hr',
    start_time: getTimeOnly(data.startTime),
    end_time: getTimeOnly(data.endTime),
    break_time: data.breaks.map((b: any) => ({
      start_time: getTimeOnly(b.breakStartTime),
      end_time: getTimeOnly(b.breakEndTime),
    })),
    departments: data.departments || [],
  };
}
