import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'; // Import fakeAsync and tick
import { AddEditShiftComponent } from './add-edit-shift.component';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { DateAdapter, provideNativeDateAdapter } from '@angular/material/core';
import { CoreModule } from '../../../../core/core.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTimepickerModule } from '@angular/material/timepicker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Mock MatDialogRef
const mockMatDialogRef = {
  close: jasmine.createSpy('close'),
};

const mockDialogData = {
  source: {
    name: '',
    startTime: '',
    endTime: '',
    format24hr: true,
    breaks: [],
    departments: [],
  },
  departmentList: [], // Ensure this is provided
};

describe('AddEditShiftComponent', () => {
  let component: AddEditShiftComponent;
  let fixture: ComponentFixture<AddEditShiftComponent>;

  beforeEach(fakeAsync(async () => {
    // <--- Wrap in fakeAsync
    await TestBed.configureTestingModule({
      declarations: [AddEditShiftComponent],
      imports: [
        BrowserAnimationsModule,
        MatDialogModule,
        CoreModule,
        FormsModule,
        ReactiveFormsModule,
        MatSlideToggleModule,
        MatSelectModule,
        MatTimepickerModule,
        MatFormFieldModule,
        MatInputModule,
        // Add any other Material modules if you use them in the template:
        // MatButtonModule, MatIconModule, etc.
      ],
      providers: [
        provideNativeDateAdapter(),
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddEditShiftComponent);
    component = fixture.componentInstance;

    // Use tick() to let microtasks complete, especially after constructor runs
    // and before the first change detection that renders the template.
    // A small tick (e.g., 50ms) can sometimes help settle things.
    tick(50); // <--- Add a tick here

    fixture.detectChanges(); // First change detection after initial setup and tick()
    // If the error still persists, uncomment this second tick and detectChanges
    // tick();
    // fixture.detectChanges();
  })); // <--- Close fakeAsync here

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
