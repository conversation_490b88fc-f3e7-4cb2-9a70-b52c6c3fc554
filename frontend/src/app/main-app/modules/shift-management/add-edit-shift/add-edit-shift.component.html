<label>
  {{ data.title }}
</label>

<div class="modal-content">
  <!-- Dialog Content -->
  <div mat-dialog-content>
    <form [formGroup]="shiftForm" class="dialog-form">
      <label class="form-label"
        >Shift Name <span class="required">*</span></label
      >
      <mat-form-field appearance="outline" class="full-width-field">
        <span matPrefix class="prefix-icon">
          <mat-icon>schedule</mat-icon>
        </span>
        <input
          matInput
          placeholder="Enter shift name"
          formControlName="shiftName"
          required
        />
        <mat-error *ngIf="shiftForm.get('shiftName')?.hasError('required')"
          >Shift name is required.
        </mat-error>
        <mat-error *ngIf="shiftForm.get('shiftName')?.hasError('minlength')">
          Shift name must be at least 2 characters
        </mat-error>
      </mat-form-field>

      <label class="form-label"
        >Select Time Format<span class="required">*</span></label
      >
      <div class="toggle-group">
        <span class="mat-body-2">12 hrs.</span>
        <mat-slide-toggle
          formControlName="is24hr"
          color="primary"
          class="small-toggle"
          (change)="onFormatChange($event)"
        ></mat-slide-toggle>
        <span class="mat-body-2">24 hrs.</span>
      </div>

      <!-- Time Selection Fields -->
      <div class="time-selection-container">
        <label class="form-label"
          >Select Time <span class="required">*</span></label
        >
        <div class="time-inputs">
          <mat-form-field appearance="outline" class="time-input first">
            <input
              #startTime
              matInput
              formControlName="startTime"
              [matTimepicker]="picker1"
              placeholder="Start time"
              [matTimepickerMax]="endTime.value"
            />
            <span matSuffix class="prefix-icon">
              <mat-icon>schedule</mat-icon>
            </span>
            <mat-timepicker #picker1 />
            <mat-error *ngIf="shiftForm.get('startTime')?.hasError('required')"
              >Start time is required.
            </mat-error>
            @if (shiftForm.get('startTime')?.hasError('matTimepickerParse')) {
              <mat-error>Invalid shift start time</mat-error>
            }

            @if (shiftForm.get('startTime')?.hasError('matTimepickerMin')) {
              <mat-error>Invalid shift start time</mat-error>
            }

            @if (shiftForm.get('startTime')?.hasError('matTimepickerMax')) {
              <mat-error>Invalid shift start time</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="time-input">
            <input
              #endTime
              matInput
              formControlName="endTime"
              [matTimepicker]="picker2"
              placeholder="End time"
              [matTimepickerMin]="startTime.value"
            />
            <span matSuffix class="prefix-icon">
              <mat-icon>schedule</mat-icon>
            </span>
            <mat-timepicker #picker2 />
            <mat-error *ngIf="shiftForm.get('endTime')?.hasError('required')"
              >End time is required.
            </mat-error>
            @if (shiftForm.get('endTime')?.hasError('matTimepickerParse')) {
              <mat-error>Invalid shift end time</mat-error>
            }

            @if (shiftForm.get('endTime')?.hasError('matTimepickerMin')) {
              <mat-error>Invalid shift end time</mat-error>
            }

            @if (shiftForm.get('endTime')?.hasError('matTimepickerMax')) {
              <mat-error>Invalid shift end time</mat-error>
            }
          </mat-form-field>
        </div>
      </div>

      <!-- Break Time Section -->
      <div formArrayName="breaks">
        <label class="form-label"
          >Add Break-time <span class="required">*</span></label
        >
        <div
          *ngFor="let breakGroup of breaks.controls; let i = index"
          [formGroupName]="i"
          class="break-time-inputs"
        >
          <mat-form-field
            appearance="outline"
            class="time-input first"
            [ngClass]="{ 'disabled-input': !startTime.value || !endTime.value }"
          >
            <input
              #breakStartTime
              matInput
              formControlName="breakStartTime"
              [matTimepicker]="picker3"
              placeholder="Start time"
              [matTimepickerMin]="startTime.value"
              [matTimepickerMax]="endTime.value"
              [disabled]="!endTime.value || !startTime.value"
            />
            <span matSuffix class="prefix-icon">
              <mat-icon>schedule</mat-icon>
            </span>
            <mat-timepicker #picker3 />
            @if (
              breakGroup.get('breakStartTime')?.hasError('matTimepickerParse')
            ) {
              <mat-error>Invalid break start time</mat-error>
            }

            @if (
              breakGroup.get('breakStartTime')?.hasError('matTimepickerMin')
            ) {
              <mat-error>Invalid break start time</mat-error>
            }

            @if (
              breakGroup.get('breakStartTime')?.hasError('matTimepickerMax')
            ) {
              <mat-error>Invalid break start time</mat-error>
            }
          </mat-form-field>

          <mat-form-field
            appearance="outline"
            class="time-input"
            [ngClass]="{ 'disabled-input': !startTime.value || !endTime.value }"
          >
            <input
              #breakEndTime
              matInput
              formControlName="breakEndTime"
              [matTimepicker]="picker4"
              placeholder="End time"
              [matTimepickerMin]="
                getValidTime(startTime.value, breakStartTime.value)
              "
              [matTimepickerMax]="endTime.value"
              [disabled]="!endTime.value || !startTime.value"
            />
            <span matSuffix class="prefix-icon">
              <mat-icon>schedule</mat-icon>
            </span>
            <mat-timepicker #picker4 />

            @if (
              breakGroup.get('breakEndTime')?.hasError('matTimepickerParse')
            ) {
              <mat-error>Invalid break end time</mat-error>
            }

            @if (breakGroup.get('breakEndTime')?.hasError('matTimepickerMin')) {
              <mat-error>Invalid break end time</mat-error>
            }

            @if (breakGroup.get('breakEndTime')?.hasError('matTimepickerMax')) {
              <mat-error>Invalid break end time</mat-error>
            }
          </mat-form-field>

          <button
            *ngIf="i > 0"
            mat-icon-button
            (click)="removeBreak(i)"
            class="remove-break-button"
            aria-label="Remove break time"
          >
            <mat-icon>remove_circle_outline</mat-icon>
          </button>
        </div>
      </div>
      <button
        type="button"
        mat-button
        color="primary"
        (click)="addBreak()"
        class="add-more-button"
        [disabled]="breaks.length >= 4"
      >
        <mat-icon>add</mat-icon>
        Add more
      </button>

      <br />
      <br />
      <!-- Department Selection -->
      <label class="form-label"
        >Select Department(s) <span class="required">*</span></label
      >
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-select formControlName="departments" multiple>
          <mat-option
            *ngFor="let department of allDepartments"
            [value]="department.id"
            >{{ department.name }}</mat-option
          >
        </mat-select>
        <mat-error *ngIf="shiftForm.get('departments')?.hasError('required')"
          >Department selection is required.
        </mat-error>
      </mat-form-field>

      <!-- Selected Department Chips -->
      <mat-chip-grid
        #chipGrid
        aria-label="Selected departments"
        *ngIf="selectedDepartments.length > 0"
      >
        <mat-chip-row
          *ngFor="let department of selectedDepartments"
          (removed)="removeDepartment(department)"
        >
          {{ getDpName(department) }}
          <button matChipRemove [attr.aria-label]="'remove ' + department">
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip-row>
      </mat-chip-grid>
    </form>
  </div>
</div>
<div
  mat-dialog-actions
  class="modal-action"
  [ngClass]="{ 'edit-mode': data.isEdit }"
>
  <button
    *ngIf="data.isEdit"
    mat-stroked-button
    class="action-button danger-button"
    color="warn"
    (click)="onDelete()"
  >
    Delete
  </button>
  <div class="ri-btns">
    <button
      mat-stroked-button
      class="action-button"
      color="primary"
      (click)="dismiss()"
    >
      Cancel
    </button>
    <button
      mat-flat-button
      class="action-button"
      color="primary"
      tabindex="-1"
      [disabled]="!shiftForm.valid"
      (click)="onSubmit()"
    >
      {{ data.isEdit ? 'Update' : 'Save' }}
    </button>
  </div>
</div>
