.modal-action {
  width: 100%;
}
.modal-action.edit-mode {
  justify-content: space-between;
}
label {
  padding: 20px;
  font-family: MontserratSemiBold;
  font-size: 20px;
}
.dialog-form {
  margin-top: -20px;
}

hr {
  border: 1px solid red;
  border-color: var(--mat-sys-primary-container);
}
p {
  margin-top: 12px;
  margin-right: 36px;
}

button {
  border-radius: 6px;
  position: relative;
  top: 5px;
}

.txt-center {
  text-align: center;
  line-height: 22px;
  margin: 0px;
}

.form-label {
  display: block;
  font-size: 16px;
  padding: 0px;
  margin-bottom: 10px;
}

.required {
  color: #ff1b1b;
}
.error-message {
  color: #ff1b1b;
  font-size: 12px;
}
mat-form-field mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  margin-left: 10px;
  margin-right: 10px;
}
.time-selection-container {
  margin-top: 20px;
}
.time-input.first {
  margin-right: 30px;
}
.full-width-field {
  width: 62%;
}
.action-button {
  width: 150px;
}
.action-button:first-child {
  margin-right: 20px;
}
.danger-button {
  border: 1px solid #ff1b1b;
  color: #ff1b1b;
}
mat-error {
  margin-left: -16px;
}
