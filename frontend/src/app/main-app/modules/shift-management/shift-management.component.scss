.title {
  padding: 15px 0px;
  font-size: 20px;
}
.title label {
  font-family: MontserratSemiBold;
}

.action-block {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
  margin-top: -25px;
}

.shift-management-container {
  padding: 24px;
}

.toolbar-spacer {
  flex: 1 1 auto;
}

.shifts-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
  height: 70vh;
}

.shift-card {
  position: relative;
  padding: 15px 20px;
  flex-direction: column;
  gap: 12px;

  & .edit-card-icon {
    position: absolute;
    right: 30px;
    top: 26px;
  }
}

.shift-card .card-block {
  display: flex;
  height: -webkit-fill-available;
  padding: 15px 20px;
  flex-direction: column;
  align-items: flex-start;
  gap: 7px;
  flex-shrink: 0;
  background-color: var(--mat-sys-surface);
  border-radius: 12px;
  border: 1px solid var(--Aluminum-Gray, #dcdcdc);

  & .shift-heading,
  .shift-time,
  .shift-d-heading {
    padding: 8px;
    gap: 10px;
    font-size: 18px;
    display: block;
    width: 95%;
  }

  & .shift-d-heading {
    border-bottom: 1px solid #b9b9b9;
  }

  & .shift-d-lists {
    display: block;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
  }

  & .shift-d-lists .d-item {
    font-size: 15px;
    padding: 8px;
    gap: 10px;
    position: relative;
    border-bottom: 1px solid #b9b9b9;
    margin-bottom: 8px;

    & button {
      position: absolute;
      right: 10px;
      top: -4px;
    }
  }
}
.d-name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
