import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { departmenturl, shiftUrl } from '../../../../app.constant';
import { IShiftResponse } from '../modal/shift-mgmt.modal';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ShiftManagementService {
  constructor(private http: HttpClient) {}

  getShifts(): Observable<IShiftResponse> {
    return this.http.get<IShiftResponse>(shiftUrl + 'list');
  }

  getDepartments(): any {
    return this.http.get(departmenturl + 'list');
  }

  addShift(shift: any): Observable<IShiftResponse> {
    return this.http.post<IShiftResponse>(shiftUrl + 'create', shift);
  }

  updateShift(shift: any, shiftId: string): Observable<IShiftResponse> {
    return this.http.put<IShiftResponse>(shiftUrl + 'update/' + shiftId, shift);
  }

  deleteShift(id: string): Observable<IShiftResponse> {
    return this.http.delete<IShiftResponse>(shiftUrl + 'delete/' + id);
  }
}
