import { TestBed } from '@angular/core/testing';

import { ShiftManagementService } from './shift-management.service';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('ShiftManagementService', () => {
  let service: ShiftManagementService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(ShiftManagementService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
