export interface IShiftResponse {
  status: boolean;
  data: IShift[];
  error_code: string | number | null;
  errors: any[];
  message: string;
}

export interface IShift {
  id: string;
  shift_name: string;
  time_format: '12hr' | '24hr';
  start_time: string;
  end_time: string;
  break_time: IBreakTime[];
  departments: string[];
}

export interface IBreakTime {
  start_time: string; // e.g. "03:00 AM"
  end_time: string; // e.g. "03:00 AM"
}

export interface IShiftDialogResult {
  action: 'save' | 'update' | 'delete';
  data: IShift;
}

export interface IShiftDialogData {
  departmentList: IShiftDepartment[];
  title: string;
  isEdit: boolean;
  source: IShiftSource;
  maxWidth?: string;
  width?: string;
  panelClass?: string;
}

export interface IShiftDepartment {
  id: string;
  name: string;
}

export interface IShiftSource {
  id: number | string;
  name: string;
  startTime: string;
  endTime: string;
  format24hr: boolean;
  departments: string[];
}
