<div class="container">
  <div class="title">
    <label>Shift Management</label>
  </div>

  <div class="containt-area">
    <cnc-loader [show]="loader" />
    <div class="action-block" *ngIf="currentUserRole !== 'admin'">
      <button
        mat-flat-button
        color="primary"
        class="no-round"
        (click)="onAddShift()"
        [disabled]="shifts.length >= 3"
      >
        <mat-icon>add</mat-icon>
        Add Shift
      </button>
    </div>

    <div class="shifts-grid">
      <!-- Loop through each shift and create a card -->
      <mat-card *ngFor="let shift of shifts" class="shift-card">
        <button
          mat-icon-button
          class="edit-card-icon"
          *ngIf="currentUserRole !== 'admin'"
          (click)="onEditShift(shift)"
        >
          <img src="icons/edit-pencil.svg" alt="Edit Shift Icon" />
        </button>

        <div class="card-block">
          <div class="shift-heading">
            <b>Shift: </b><span>{{ shift.shift_name }}</span>
          </div>
          <div class="shift-time">
            <b>Time: </b
            ><span>{{ shift.start_time }} - {{ shift.end_time }}</span>
          </div>
          <div class="shift-d-heading">
            <b>Departments</b>
          </div>
          <div class="shift-d-lists">
            <div class="d-item" *ngFor="let department of shift.departments">
              <span
                class="d-name"
                matTooltip="{{ getDpName(department) || '' }}"
                >{{ getDpName(department) }}</span
              >
              <button
                mat-icon-button
                aria-label="Remove department"
                [disabled]="
                  currentUserRole === 'admin' || shift.departments.length <= 1
                "
                (click)="removeDepartment(shift, department)"
              >
                <mat-icon>cancel</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
</div>
