import { Component, inject, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AddEditShiftComponent } from './add-edit-shift/add-edit-shift.component';
import { ShiftManagementService } from './services/shift-management.service';
import {
  IShift,
  IShiftDialogResult,
  IShiftResponse,
} from './modal/shift-mgmt.modal';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { AuthService } from '../../../core/services/auth.service';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'cnc-shift-management',
  standalone: false,
  templateUrl: './shift-management.component.html',
  styleUrl: './shift-management.component.scss',
})
export class ShiftManagementComponent implements OnInit {
  shifts: IShift[] = [];
  departmentList: any = [];
  loader = true;
  currentUserRole = inject(AuthService).currentUser?.role;

  constructor(
    private dialog: MatDialog,
    private shiftManagementService: ShiftManagementService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit() {
    this.getShifts();
    this.getDepartments();
  }

  getShifts() {
    this.shiftManagementService.getShifts().subscribe({
      next: (res: IShiftResponse) => {
        this.shifts = res.data as IShift[];
        this.loader = false;
      },
      error: (error: any) => this.onError('Error fetching shifts:', error),
    });
    this.loader = false;
  }

  getDepartments() {
    this.shiftManagementService.getDepartments().subscribe({
      next: (res: any) => {
        this.departmentList = res.data.map((dept: any) => {
          return {
            id: dept.id,
            name: dept.name,
          };
        });
      },
      error: (error: any) => this.onError('Error fetching departments:', error),
    });
    this.loader = false;
  }
  removeDepartment(shift: IShift, department: string): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Confirmation',
        message: `Are you sure you want to remove this department from ${shift.shift_name} shift?`,
        cancelButtonName: 'Cancel',
        okButtonName: 'Ok',
      },
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((dialogResult: boolean) => {
      if (dialogResult) {
        const index = shift.departments.indexOf(department);
        if (index >= 0) {
          shift.departments.splice(index, 1);
        }
        const { id, created_at, updated_at, ...shiftData } = shift as any;
        this.editShift(shiftData, id);
      }
    });
  }

  getDpName(dpId: string): string {
    return this.departmentList.find((dp: any) => dp.id === dpId)?.name || '';
  }

  onAddShift(): void {
    const dialogRef = this.dialog.open(AddEditShiftComponent, {
      data: {
        departmentList: this.departmentList,
        title: 'Add Shift',
        isEdit: false,
        source: {
          id: 0,
          name: '',
          startTime: '',
          endTime: '',
          format24hr: true,
          departments: [],
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((result: IShiftDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.addShift(data);
      }
    });
  }

  addShift(data: IShift): void {
    this.loader = true;
    this.shiftManagementService.addShift(data).subscribe({
      next: (res: IShiftResponse) => {
        this.getShifts();
        this.onSucess('Shift added successfully!');
      },
      error: (error: any) => {
        this.loader = false;
        this.reOpenDialog(data, false);
        if (error.status === 409) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 400) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 500) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        this.onError('Error updating shift:', error);
      },
    });
  }

  reOpenDialog(dataobj: IShift, isEdit: boolean): void {
    const dialogRef = this.dialog.open(AddEditShiftComponent, {
      data: {
        departmentList: this.departmentList,
        title: isEdit ? 'Edit Shift' : 'Add Shift',
        isEdit: isEdit,
        source: {
          id: dataobj.id || 0,
          name: dataobj.shift_name || '',
          startTime: dataobj.start_time || '',
          endTime: dataobj.end_time || '',
          format24hr: dataobj.time_format === '24hr',
          breaks: dataobj.break_time.map((b) => ({
            breakStartTime: b.start_time,
            breakEndTime: b.end_time,
          })),
          departments: dataobj.departments || [],
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });
    dialogRef.afterClosed().subscribe((result: IShiftDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'save') {
        this.addShift(data);
      }
      if (action === 'update') {
        this.loader = true;
        this.editShift(data, dataobj.id);
      } else if (action === 'delete') {
        this.loader = true;
        this.deleteShift(dataobj.id);
      }
    });
  }

  onEditShift(data: IShift): void {
    const shift: IShift = JSON.parse(JSON.stringify(data));
    const dialogRef = this.dialog.open(AddEditShiftComponent, {
      data: {
        departmentList: this.departmentList,
        title: 'Edit Shift',
        isEdit: true,
        source: {
          id: shift.id,
          name: shift.shift_name,
          startTime: shift.start_time,
          endTime: shift.end_time,
          format24hr: shift.time_format === '24hr',
          breaks: shift.break_time.map((b) => ({
            breakStartTime: b.start_time,
            breakEndTime: b.end_time,
          })),
          departments: shift.departments,
        },
      },
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((result: IShiftDialogResult) => {
      if (!result) return;
      const { action, data } = result;
      if (action === 'update') {
        this.loader = true;
        this.editShift(data, shift.id);
      } else if (action === 'delete') {
        this.loader = true;
        this.deleteShift(shift.id);
      }
    });
  }

  editShift(shift: Partial<IShift>, shiftId: string): void {
    this.shiftManagementService.updateShift(shift, shiftId).subscribe({
      next: (res: IShiftResponse) => {
        this.getShifts();
        this.onSucess('Shift updated successfully!');
      },
      error: (error: any) => {
        this.loader = false;
        shift.id = shiftId; // Ensure the shift ID is set for re-opening the dialog
        this.reOpenDialog(shift as IShift, true);
        if (error.status === 409) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 400) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        if (error.status === 500) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        }
        this.onError('Error updating shift:', error);
      },
    });
  }

  deleteShift(shiftId: string): void {
    this.shiftManagementService.deleteShift(shiftId).subscribe({
      next: (res: IShiftResponse) => {
        this.getShifts();
        this.snackbarService.open(res.message, '', 'success', 3000);
      },
      error: (error: any) => {
        this.loader = false;
        if (error.status === 400 || error.status === 500) {
          this.snackbarService.open(error.error.message, '', 'failed', 3000);
        } else {
          this.snackbarService.open(
            'An unexpected error occurred.',
            '',
            'failed',
            3000
          );
        }
      },
    });
  }

  onSucess(message: string) {
    this.snackbarService.open(message, '', 'success', 3000);
  }

  onError(message: string, error: any) {
    this.loader = false;
    console.error(message, error);
  }
}
