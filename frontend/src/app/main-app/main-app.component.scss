.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .hamburger {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1001;
  }

  .main-content {
    flex: 1;
    padding: 16px;
    margin-left: 70px; // Adjust for collapsed sidebar width
    transition: margin 0.3s ease;
    padding-top: 60px;

    &.expanded-sidebar {
      margin-left: 300px; // When sidebar collapsed
    }

    &.full-screen {
      margin-left: 0px;
    }
    // When sidebar expanded
    body.expanded-sidebar & {
      margin-top: 60px; /* navbar height */
      margin-left: 240px;
    }
  }
}

.body-container {
  display: flex;
  flex: 1; /* Grow to fill space below header */
  overflow: hidden; /* Prevent inner scrollbars */
}
