import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainAppComponent } from './main-app.component';
import { MainAppRoutingModule } from './main-app-routing.module';
import { SharedModule } from '../shared/shared.module';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
@NgModule({
  declarations: [MainAppComponent],
  imports: [
    CommonModule,
    MainAppRoutingModule,
    SharedModule,
    MatToolbarModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
  ],
})
export class MainAppModule {}
