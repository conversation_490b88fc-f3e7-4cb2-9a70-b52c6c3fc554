import { Component, OnInit, Renderer2 } from '@angular/core';
import { SnackbarService } from '../shared/services/snackbar.service';
import { SettingsService } from './modules/settings/settings.service';
import {
  ISettings,
  ISettingsPayload,
} from './modules/settings/model/settings.model';
import { imageBasePath } from '../app.constant';

@Component({
  selector: 'cnc-main-app',
  standalone: false,
  templateUrl: './main-app.component.html',
  styleUrl: './main-app.component.scss',
})
export class MainAppComponent implements OnInit {
  collapsed = true;
  fullScreenMode = false;
  constructor(
    private renderer: Renderer2,
    private snackbarService: SnackbarService,
    private settingsService: SettingsService
  ) {
    this.settingsService.fullScreenMode$.subscribe(
      (mode) => (this.fullScreenMode = mode)
    );
  }
  ngOnInit(): void {
    this.applyThemeFromSettings();
  }
  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }
  private applyThemeFromSettings(): void {
    this.settingsService.getSettings().subscribe({
      next: (res) => {
        if (res.status && res.data.length > 0) {
          const settings: ISettings = res.data[0];
          this.settingsService.systemSettings = settings;
          this.settingsService.logoImg.set(
            settings.logo_path ? imageBasePath + settings.logo_path : null
          );
          const theme = settings.theme?.trim() || 'blue-theme';

          const body = document.body;

          // Remove any existing theme class ending with '-theme'
          const currentTheme = Array.from(body.classList).find((cls) =>
            cls.endsWith('-theme')
          );
          if (currentTheme) {
            this.renderer.removeClass(body, currentTheme);
          }

          // Apply the new theme
          this.renderer.addClass(body, theme);

          // Optional: store theme locally for reuse
          localStorage.setItem('active_theme', theme);
        }
      },
      error: () => {
        // this.snackbarService.open('Failed to load theme.', 'X', 'error');
      },
    });
  }
}
