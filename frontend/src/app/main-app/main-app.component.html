<!-- <div class="container" style="position: relative">
  <p>main-app works!</p>
<cnc-loader [show]="true"></cnc-loader>
  <router-outlet />
</div>
<cnc-header (toggleMenu)="toggleSidebar()"></cnc-header> -->
<div class="layout-container">
  <cnc-header (toggleMenu)="toggleSidebar()"></cnc-header>
  <div class="body-container">
    <cnc-sidebar [collapsed]="collapsed" *ngIf="!fullScreenMode"></cnc-sidebar>
    <div
      class="main-content"
      [ngClass]="{
        'expanded-sidebar': !collapsed,
        'full-screen': fullScreenMode,
      }"
    >
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
