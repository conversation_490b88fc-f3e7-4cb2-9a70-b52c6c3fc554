import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { catchError, map } from 'rxjs/operators';
import { of } from 'rxjs';

export const AuthGuard: CanActivateFn = (route, state) => {
  const router: Router = inject(Router);
  const auth = inject(AuthService);
  const token = auth.token;
  // Optionally, implement a method isTokenValid() in AuthService for better validation
  const isLoggedIn = auth.isLoggedIn;
  if (token && isLoggedIn) {
    return true;
  }

  // Token is invalid or expired — try to refresh it
  return auth.refreshAccessToken().pipe(
    map((response) => {
      const newToken = response?.data[0].accessToken;
      if (newToken) {
        auth.setToken(newToken);
        return true;
      } else {
        router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
        return false;
      }
    }),
    catchError(() => {
      router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return of(false);
    })
  );

  return router.parseUrl('login');
};
