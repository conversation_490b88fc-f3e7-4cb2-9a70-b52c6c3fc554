import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { LicenseActivateGuard } from './license-activate.guard';

describe('licenseActivateGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) =>
    TestBed.runInInjectionContext(() =>
      LicenseActivateGuard(...guardParameters)
    );

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
