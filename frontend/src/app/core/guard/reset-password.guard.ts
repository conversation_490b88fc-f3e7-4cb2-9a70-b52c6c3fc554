import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';

import { AuthService } from '../services/auth.service';
export const ResetPasswordGuard: CanActivateFn = (route, state) => {
  const router: Router = inject(Router);
  const auth = inject(AuthService);
  if (auth.currentUser?.isPasswordReset) {
    return true;
  } else {
    return router.parseUrl('login');
  }
};
