import { inject } from '@angular/core';
import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpInterceptorFn,
  HttpRequest,
} from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { catchError, switchMap, throwError, of } from 'rxjs';

export const authInterceptor: HttpInterceptorFn = (
  req: HttpRequest<any>,
  next: HttpHandlerFn
) => {
  const authService = inject(AuthService);
  const isLoggedIn = authService.isLoggedIn;
  const token = authService.token;

  let authReq = req;

  // Attach token if available
  if (token) {
    authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  // Proceed with the request
  return next(authReq).pipe(
    catchError((error: HttpErrorResponse) => {
      // Avoid infinite refresh loops
      const isUnauthorized = error.status === 401;
      const isRefreshEndpoint = req.url.includes('/refresh-token');

      if (isUnauthorized && !isRefreshEndpoint && isLoggedIn) {
        return authService.refreshAccessToken().pipe(
          switchMap((response: any) => {
            const newToken = response?.data[0].accessToken;

            if (newToken) {
              // Update token in service
              authService.setToken(newToken);

              // Retry the original request with new token
              const retryReq = req.clone({
                setHeaders: {
                  Authorization: `Bearer ${newToken}`,
                },
              });

              return next(retryReq);
            }

            // If token is missing in response
            return throwError(
              () => new Error('No access token in refresh response')
            );
          }),
          catchError((refreshError) => {
            // If refresh also fails, log out user
            // authService.logout().subscribe({
            //   error: (err) => console.error('Logout error', err),
            // });
            return throwError(() => refreshError);
          })
        );
      }

      // If error is not 401 or already handled, just pass through
      return throwError(() => error);
    })
  );
};
