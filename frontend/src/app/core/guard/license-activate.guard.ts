import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { LicenseService } from '../services/license.service';

export const LicenseActivateGuard: CanActivateFn = (route, state) => {
  const router: Router = inject(Router);
  const licenseService = inject(LicenseService);
  if (licenseService.validLicense) {
    return true;
  } else {
    return router.parseUrl('licence-activation');
  }
};
