function convertToJsonFile(data: any) {
  const jsonString = JSON.stringify(data);
  const blob = new Blob([jsonString], { type: 'application/json' });
  return blob;
}

export function downloadRequestObject(data: any, fileName: string) {
  const blob = convertToJsonFile(data);
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${fileName}.json`;
  a.click();
  window.URL.revokeObjectURL(url);
}

export function base64Encode(str: string): string {
  const utf8Bytes = new TextEncoder().encode(str);
  let binary = '';
  utf8Bytes.forEach((b) => (binary += String.fromCharCode(b)));
  return btoa(binary);
}

export function base64Decode(base64: string): string {
  const binary = atob(base64);
  const bytes = new Uint8Array([...binary].map((char) => char.charCodeAt(0)));
  return new TextDecoder().decode(bytes);
}

export function getTimeOnly(date: string) {
  const datenow = new Date(date);
  const options: any = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  return datenow.toLocaleTimeString('en-US', options);
}

export function parseTimeToToday(timeStr: string) {
  const [time, modifier] = timeStr.split(' '); // "10:30", "AM"
  const res = time.split(':').map(Number); // 10, 30
  let hours = res[0];
  const minutes = res[1];
  if (modifier === 'PM' && hours !== 12) hours += 12;
  if (modifier === 'AM' && hours === 12) hours = 0;

  const now = new Date();
  return new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    hours,
    minutes,
    0
  );
}
