import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { jwtDecode } from 'jwt-decode';
import { authUrl, userUrl } from '../../app.constant';

export interface Jwt {
  exp: number;
  scope: string[];
  sub: string;
  name: string;
}

export interface User {
  userId: string;
  isPasswordReset: boolean;
  username: string;
  firstname: string | null;
  lastname: string | null;
  email: string | null;
  role: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  user: User | null = null;
  constructor(private http: HttpClient) {}

  get currentUser(): User | null {
    if (this.user) {
      return this.user;
    }
    const user = localStorage.getItem('user');
    if (user) {
      this.user = JSON.parse(user);
      return this.user;
    }
    return null;
  }

  get token(): string | null {
    return localStorage.getItem('token');
  }

  get isLoggedIn() {
    const token = this.token;
    if (!token) {
      return false;
    }
    return tokenIsValid(token);
  }
  get refresh(): string | null {
    return localStorage.getItem('refresh_token');
  }
  login(username: string, password: string): Observable<any> {
    return this.http.post(authUrl + 'login', { username, password });
  }
  resetPassword(username: string, password: string): Observable<any> {
    return this.http.post(authUrl + 'reset-password', { username, password });
  }
  profile() {
    return this.http.get(userUrl + 'profile');
  }
  logout(): Observable<any> {
    const token = this.token;
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    this.user = null;
    localStorage.removeItem('user');
    localStorage.removeItem('mode');
    localStorage.removeItem('lic-info');
    return this.http.post(authUrl + 'logout', { token });
  }

  refreshAccessToken(): Observable<any> {
    const refreshToken = this.refresh;
    if (!refreshToken) return of(null);
    return this.http.post(`${authUrl}refresh-token`, { token: refreshToken });
  }

  setToken(token: string, refreshToken?: string, user?: User) {
    this.user = user || this.user;
    if (tokenIsValid(token)) {
      localStorage.setItem('token', token);
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken);
      }
    }
    if (this.user) {
      localStorage.setItem('user', JSON.stringify(this.user));
    }
  }
}

function tokenIsValid(token: string) {
  const payload = jwtDecode(token);
  if (isJwt(payload)) {
    return isTokenTimeValid(payload);
  }
  return false;
}

function isJwt(a: any): a is Jwt {
  return typeof a === 'object' && a.exp !== undefined;
}

function isTokenTimeValid(tokenPayload: Jwt) {
  return tokenPayload.exp >= Date.now() / 1000;
}
