import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { delay, of } from 'rxjs';
import { base64Decode, base64Encode } from '../helper/functions.utility';

@Injectable({
  providedIn: 'root',
})
export class LicenseService {
  systemInfo: any = { logo: 'images/logo.png', uuid: '', serialNumber: '' };
  constructor(private http: HttpClient) {}

  get license(): string | null {
    return localStorage.getItem('lic-info');
  }

  get validLicense() {
    const license = this.license;
    if (!license) {
      return false;
    }
    return JSON.parse(base64Decode(license));
  }

  getLicenseActivatedInfo() {
    this.http.get('/api/license/activated').subscribe({
      next: (data) => this.setLicenseActivatedInfo(data),
      error: (error) =>
        console.error('Error fetching license activation status:', error),
    });
  }

  setLicenseActivatedInfo(info: any) {
    localStorage.setItem('lic-info', base64Encode(JSON.stringify(info)));
  }

  getSystemInfo() {
    // remove this line when backend is ready
    return of({
      uuid: '12345',
      serialNumber: '1.0.0',
    });
    return this.http.get('/api/v1/system/info');
  }

  activateLicense(file: File) {
    const formData: FormData = new FormData();
    formData.append('file', file, file.name);

    // remove this line when backend is ready
    this.setLicenseActivatedInfo({
      activated: true,
      expiryDate: '2025-12-31',
      licenseType: 'Pro',
      logo: 'assets/images/logo.png',
    });
    // remove this line when backend is ready
    return of(true).pipe(delay(2000));
    return this.http.post('/api/v1/license/activate', formData);
  }
}
