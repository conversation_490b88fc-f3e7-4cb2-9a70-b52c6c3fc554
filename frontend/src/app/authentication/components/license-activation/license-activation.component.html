<div class="container">
  <div class="logo-container">
    <img src="icons/shopcnc.svg" alt="myShop INC." class="logo" />
  </div>
  <div>
    <mat-card-title class="card-title">Activate license</mat-card-title>
  </div>
  <mat-card class="info-card">
    <mat-card-content>
      <p class="upload-label">Upload valid license file</p>

      <ngx-file-drop (onFileDrop)="dropped($event)">
        <ng-template
          ngx-file-drop-content-tmp
          let-openFileSelector="openFileSelector"
        >
          <div class="drop-zone-content">
            <p>{{ file?.relativePath }}</p>
            <p *ngIf="!file?.relativePath">
              Drop your file here or
              <a class="browse" (click)="openFileSelector()">Browse</a>
            </p>
          </div>
        </ng-template>
      </ngx-file-drop>
    </mat-card-content>
    <mat-card-actions class="card-actions">
      <button
        mat-stroked-button
        class="action-button secondary-button"
        (click)="getSystemInfo()"
      >
        Get System Info
      </button>
      <button
        mat-flat-button
        color="primary"
        class="action-button"
        (click)="onActivate()"
        [disabled]="!file || isLoading"
      >
        <span *ngIf="!isLoading">Activate</span>
        <mat-progress-spinner
          *ngIf="isLoading"
          color="accent"
          mode="indeterminate"
          [diameter]="20"
        ></mat-progress-spinner>
      </button>
    </mat-card-actions>
  </mat-card>
</div>
