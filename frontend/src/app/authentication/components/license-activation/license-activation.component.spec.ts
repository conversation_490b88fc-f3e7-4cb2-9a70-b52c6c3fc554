import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LicenseActivationComponent } from './license-activation.component';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { CoreModule } from '../../../core/core.module';
import { NgxFileDropModule } from 'ngx-file-drop';

describe('LicenseActivationComponent', () => {
  let component: LicenseActivationComponent;
  let fixture: ComponentFixture<LicenseActivationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreModule, NgxFileDropModule],
      declarations: [LicenseActivationComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents();

    fixture = TestBed.createComponent(LicenseActivationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
