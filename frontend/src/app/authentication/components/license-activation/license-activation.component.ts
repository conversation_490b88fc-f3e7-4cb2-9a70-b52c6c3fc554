import { Component } from '@angular/core';
import { LicenseService } from '../../../core/services/license.service';
import { downloadRequestObject } from '../../../core/helper/functions.utility';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AlertComponent } from '../../../shared/components/alert/alert.component';

@Component({
  selector: 'cnc-license-activation',
  standalone: false,
  templateUrl: './license-activation.component.html',
  styleUrl: './license-activation.component.scss',
})
export class LicenseActivationComponent {
  file: any;
  isLoading = false;
  constructor(
    public licenseService: LicenseService,
    private snackbarService: SnackbarService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  public dropped(files: NgxFileDropEntry[]) {
    const [file] = files;
    const ext = file.relativePath.split('.').pop()?.toLowerCase();
    if (ext === 'lic') {
      this.file = file;
    } else {
      this.snackbarService.open(
        'Please upload a valid licence file (.lic)',
        'X',
        'failed',
        3000
      );
    }
  }

  onActivate() {
    this.isLoading = true;
    if (this.file.fileEntry.isFile) {
      const fileEntry = this.file.fileEntry as FileSystemFileEntry;
      fileEntry.file((file: File) => {
        this.licenseService.activateLicense(file).subscribe({
          next: (res) => this.router.navigate(['app', 'dashboard']),
          error: (error) =>
            this.snackbarService.open(
              'Invalid licence file',
              'X',
              'failed',
              3000
            ),
        });
      });
    }
  }

  getSystemInfo() {
    this.licenseService.getSystemInfo().subscribe({
      next: (data) => {
        downloadRequestObject(data, 'systemInfo');
        const dialogRef = this.dialog.open(AlertComponent, {
          data: {
            title: 'Information',
            message:
              'Downloaded system info file send to AWM admin for get valid license file for activate your application',
            buttonName: 'Close',
            icon: 'thumb_up',
          },
          panelClass: 'alert-dialog',
        });
        dialogRef.afterClosed().subscribe();
      },
      error: (error) => {
        console.error('Error fetching system info:', error);
      },
    });
  }
}
