:host {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f7f8fa;
  font-family: 'Roboto', sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: -80px;
}

.logo-container {
  margin-bottom: 30px;
}

.logo {
  width: 150px; /* Adjust size as needed */
  height: auto;
}

.info-card {
  max-width: 450px;
  width: 100%;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px;
  background-color: var(--primary-bg);
}

.card-title {
  text-align: center;
  width: 100%;
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
}

.upload-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0 0 0 !important; /* Override default padding */
  gap: 10px;
}

.action-button {
  border-radius: 6px;
  padding: 8px 20px;
  text-transform: none;
  font-size: 14px;
  width: 100%;
  margin: 0px 17px;
}

.secondary-button {
  border-color: #dce3f1;
  color: #333;
}
.drop-zone-content {
  color: #888;
}

.browse {
  color: var(--mat-sys-primary);
  font-weight: 600;
  cursor: pointer;
}
::ng-deep .ngx-file-drop__drop-zone[_ngcontent-ng-c3607569390] {
  height: 100px;
  margin: auto;
  border: 2px dotted #0782d0;
  border-color: var(--mat-sys-primary);
  border-radius: 11px !important;
  background-color: var(--gray-bg);
}
