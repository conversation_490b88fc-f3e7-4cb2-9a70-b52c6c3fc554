<!-- src/app/login/login.component.html -->
<div class="login-container">
  <div class="login-split-wrapper">
    <!-- Left Section: Logo and Text -->
    <div class="left-section">
      <div class="logo-area">
        <!-- Using a simple SVG circle and text for the logo -->
        <img
          [src]="licenseService.systemInfo.logo"
          class="logo"
          alt="myShopCNC Logo"
        />
      </div>
      <p class="section-text">
        Welcome to myShopcnc the only data acquisition system built by
        manufacturers who understand your daily challenges.
      </p>
      <p class="section-text">
        You're about to transform fragmented machine data into clear insights
        that deliver measurable results. By unifying all your equipment – from
        legacy mills to modern CNCs - into one intelligent network, you'll
        expose hidden bottlenecks, prevent costly downtime, and maximize every
        shift.
      </p>
      <p class="section-text">
        As you log in, prepare to unlock stranded capacity, replace assumptions
        with facts, and scale your insights with affordability and ease.
      </p>
      <p class="section-text">This is your shop... optimized.</p>
    </div>

    <!-- Right Section: Login Form -->
    <div class="right-section">
      <mat-card class="login-form-card">
        <mat-card-title class="card-title"
          >Please reset your username and password</mat-card-title
        >
        <mat-card-content>
          <form [formGroup]="resetForm" (ngSubmit)="onSubmit()" novalidate>
            <!-- Username -->
            <label class="form-label"
              >New Username <span class="required">*</span></label
            >
            <mat-form-field appearance="outline" class="full-width-input">
              <mat-icon matPrefix>person_outline</mat-icon>
              <input matInput formControlName="username" />
              <mat-error
                *ngIf="
                  f['username'].touched && f['username'].errors?.['required']
                "
              >
                Username is required
              </mat-error>
              <mat-error
                *ngIf="
                  f['username'].touched && f['username'].errors?.['minlength']
                "
              >
                Username must be at least 3 characters long.
              </mat-error>
              <mat-error
                *ngIf="
                  f['username'].touched && f['username'].errors?.['pattern']
                "
              >
                Username must be alphanumeric.
              </mat-error>
            </mat-form-field>

            <!-- New Password -->
            <label class="form-label"
              >Enter new password <span class="required">*</span></label
            >
            <mat-form-field appearance="outline" class="full-width-input">
              <mat-icon matPrefix>lock_outline</mat-icon>
              <input
                matInput
                [type]="hidePassword ? 'password' : 'text'"
                formControlName="password"
              />
              <button
                mat-icon-button
                matSuffix
                (click)="hidePassword = !hidePassword"
                type="button"
              >
                <mat-icon>{{
                  hidePassword ? 'visibility_off' : 'visibility'
                }}</mat-icon>
              </button>
              <mat-error *ngIf="f['password'].touched && f['password'].invalid">
                <span *ngIf="f['password'].errors?.['required']"
                  >Password is required.</span
                >
                <span *ngIf="f['password'].errors?.['pattern']"
                  >Password must be 8–16 characters with 1 uppercase, 1
                  lowercase, 1 number, and 1 special character.
                </span>
              </mat-error>
            </mat-form-field>

            <!-- Confirm Password -->
            <div class="form-group">
              <label class="form-label ValidationError"
                >Confirm password <span class="required">*</span></label
              >
              <mat-form-field appearance="outline" class="full-width-input">
                <mat-icon matPrefix>lock_outline</mat-icon>
                <input
                  matInput
                  [type]="hideConfirm ? 'password' : 'text'"
                  formControlName="confirmPassword"
                />
                <button
                  mat-icon-button
                  matSuffix
                  (click)="hideConfirm = !hideConfirm"
                  type="button"
                >
                  <mat-icon>{{
                    hideConfirm ? 'visibility_off' : 'visibility'
                  }}</mat-icon>
                </button>
                <mat-error
                  *ngIf="
                    f['confirmPassword'].touched &&
                    f['confirmPassword'].errors?.['required']
                  "
                  >Confirm password is required.</mat-error
                >
                <mat-error
                  *ngIf="
                    f['confirmPassword'].touched &&
                    f['confirmPassword'].errors?.['passwordMismatch']
                  "
                >
                  Confirm password does not match.
                </mat-error>
              </mat-form-field>
            </div>
            <button
              mat-flat-button
              color="primary"
              type="submit"
              class="login-button"
              [disabled]="resetForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Reset</span>
              <mat-progress-spinner
                *ngIf="isLoading"
                color="accent"
                mode="indeterminate"
                [diameter]="20"
              ></mat-progress-spinner>
            </button>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
