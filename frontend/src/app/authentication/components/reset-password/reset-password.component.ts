import { Component } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { LicenseService } from '../../../core/services/license.service';
import { STRONGPASSWORDPATTERN } from '../../../app.constant';

@Component({
  selector: 'cnc-reset-password',
  standalone: false,
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss',
})
export class ResetPasswordComponent {
  isLoading = false;
  resetForm: FormGroup;
  hidePassword = true;
  hideConfirm = true;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private snackbarService: SnackbarService,
    public licenseService: LicenseService
  ) {
    this.resetForm = this.fb.group(
      {
        username: [
          '',
          [
            Validators.required,
            Validators.minLength(3),
            Validators.pattern(/^[a-zA-Z0-9]+$/), // only alphanumeric
          ],
        ],
        password: [
          '',
          [Validators.required, Validators.pattern(STRONGPASSWORDPATTERN)],
        ],
        confirmPassword: [
          '',
          [Validators.required, Validators.pattern(STRONGPASSWORDPATTERN)],
        ],
      },
      {
        validators: [this.passwordValidator],
      }
    );
  }
  passwordValidator(control: AbstractControl): ValidationErrors | null {
    const password = control.get('password')?.value;
    const confirmPassword = control.get('confirmPassword')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      control.get('confirmPassword')?.setErrors({ passwordMismatch: true });
    } else {
      const errors = control.get('confirmPassword')?.errors;
      if (errors && errors['passwordMismatch']) {
        delete errors['passwordMismatch'];
        if (Object.keys(errors).length === 0) {
          control.get('confirmPassword')?.setErrors(null);
        } else {
          control.get('confirmPassword')?.setErrors(errors);
        }
      }
    }

    return null;
  }

  get f() {
    return this.resetForm.controls;
  }

  onSubmit() {
    if (this.resetForm.valid) {
      const { username, password } = this.resetForm.value;

      this.isLoading = true;
      this.authService.resetPassword(username.trim(), password).subscribe({
        next: (res) => {
          if (res.error_code === 500) {
            this.isLoading = false;
            const errorMessage = res.message;
            this.snackbarService.open(errorMessage, 'X', 'failed');
            return;
          }
          this.isLoading = false;
          this.snackbarService.open(
            'Your Username and Password has been changed successfully.',
            '',
            'success',
            3000
          );
          setTimeout(() => this.router.navigate(['login']), 2000);
        },
        error: (err) => (this.isLoading = false),
      });
    }
  }
}
