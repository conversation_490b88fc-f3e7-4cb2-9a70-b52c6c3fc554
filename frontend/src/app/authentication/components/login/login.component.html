<!-- src/app/login/login.component.html -->
<div class="login-container">
  <div class="login-split-wrapper">
    <!-- Left Section: Logo and Text -->
    <div class="left-section">
      <div class="logo-area">
        <!-- Using a simple SVG circle and text for the logo -->
        <img src="icons/shopcnc.svg" class="logo" alt="myShopCNC Logo" />
      </div>
      <p class="section-text">
        Welcome to myShopcnc the only data acquisition system built by
        manufacturers who understand your daily challenges.
      </p>
      <p class="section-text">
        You're about to transform fragmented machine data into clear insights
        that deliver measurable results. By unifying all your equipment – from
        legacy mills to modern CNCs - into one intelligent network, you'll
        expose hidden bottlenecks, prevent costly downtime, and maximize every
        shift.
      </p>
      <p class="section-text">
        As you log in, prepare to unlock stranded capacity, replace assumptions
        with facts, and scale your insights with affordability and ease.
      </p>
      <p class="section-text">This is your shop... optimized.</p>
      <span class="version">myShopCNC_v0.0.4</span>
    </div>

    <!-- Right Section: Login Form -->
    <div class="right-section">
      <mat-card class="login-form-card">
        <mat-card-title class="card-title"
          >Please login to continue</mat-card-title
        >
        <mat-card-content>
          <form class="login-form" (ngSubmit)="onSubmit()" #loginForm="ngForm">
            <!-- Username Input -->
            <label class="form-label"
              >Username <span class="required">*</span></label
            >
            <mat-form-field appearance="outline" class="full-width-input">
              <input
                matInput
                required
                [(ngModel)]="username"
                name="username"
                (keyup)="clearMsg()"
                #usernameInput="ngModel"
                autocomplete="username"
              />
              <mat-icon matPrefix>person_outline</mat-icon>
              <!-- Changed from 'person' to 'person_outline' for mockup match -->
              <!-- Error message for username -->
              <mat-error
                *ngIf="
                  usernameInput.invalid &&
                  (usernameInput.dirty || usernameInput.touched)
                "
              >
                <span
                  class="error-message"
                  *ngIf="usernameInput.errors?.['required']"
                  >Username is required.</span
                >
              </mat-error>
            </mat-form-field>
            <label class="form-label"
              >Password <span class="required">*</span></label
            >
            <!-- Password Input -->
            <mat-form-field appearance="outline" class="full-width-input">
              <input
                matInput
                [type]="hidePassword ? 'password' : 'text'"
                required
                [(ngModel)]="password"
                name="password"
                (keyup)="clearMsg()"
                #passwordInput="ngModel"
                autocomplete="new-password"
              />
              <mat-icon matPrefix>lock_outline</mat-icon>
              <!-- Changed from default to 'lock_outline' for mockup match -->
              <button
                mat-icon-button
                matSuffix
                (click)="hidePassword = !hidePassword"
                [attr.aria-label]="'Toggle password visibility'"
                [attr.aria-pressed]="hidePassword"
                type="button"
              >
                <mat-icon>{{
                  hidePassword ? 'visibility_off' : 'visibility'
                }}</mat-icon>
              </button>
              <!-- Error message for password -->
              <mat-error
                *ngIf="
                  passwordInput.invalid &&
                  (passwordInput.dirty || passwordInput.touched)
                "
              >
                <span
                  *ngIf="passwordInput.errors?.['required']"
                  class="error-message"
                  >Password is required.</span
                >
              </mat-error>
            </mat-form-field>

            <mat-error class="invalid-credentials">{{
              errorMessage
            }}</mat-error>

            <!-- Remember Me Checkbox -->
            <mat-checkbox class="remember-me-checkbox"
              >Remember me</mat-checkbox
            >

            <!-- Login Button -->
            <button
              mat-flat-button
              color="primary"
              type="submit"
              class="login-button"
              [disabled]="loginForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Log In</span>
              <!-- Changed from Login to Log In for mockup match -->
              <mat-progress-spinner
                *ngIf="isLoading"
                color="accent"
                mode="indeterminate"
                [diameter]="20"
              ></mat-progress-spinner>
            </button>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
