import { Component } from '@angular/core';
import { AuthService } from '../../../core/services/auth.service';
import { Router } from '@angular/router';
import { LicenseService } from '../../../core/services/license.service';

@Component({
  selector: 'cnc-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  username = '';
  password = '';
  hidePassword = true;
  isLoading = false; // State for loading spinner
  invalidCredentials = false; // State for invalid credentials message
  locked = false; // State for locked account
  errorMessage = '';
  constructor(
    private authService: AuthService,
    private router: Router,
    public licenseService: LicenseService
  ) {
    if (this.authService.isLoggedIn) {
      this.router.navigate(['app']);
    }
  }

  onSubmit(): void {
    if (this.isLoading) {
      return; // Prevent multiple submissions
    }
    this.isLoading = true;

    this.authService.login(this.username, this.password).subscribe({
      next: (res) => {
        this.isLoading = false;

        const [{ token, refreshToken, user }] = res.data;
        this.authService.setToken(token, refreshToken, user);
        if (!user.isPasswordReset) {
          this.router.navigate(['', 'reset-password']);
        } else {
          this.router.navigate(['app', 'dashboard']);
        }
      },
      error: (error) => {
        this.errorMessage = error.error.message || 'An error occurred';
        this.isLoading = false; // Reset loading state on error
      },
    });
  }

  clearMsg(): void {
    this.errorMessage = '';
  }
}
