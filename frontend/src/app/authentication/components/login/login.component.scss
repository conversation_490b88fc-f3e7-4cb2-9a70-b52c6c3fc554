.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* Occupy full viewport height */
  background-color: var(
    --mat-card-background
  ); /* Use Angular Material background color */
  padding: 20px; /* Add some padding around the card */
  box-sizing: border-box; /* Include padding in the element's total width and height */
}

.login-split-wrapper {
  display: flex;
  flex-direction: column; /* Stacks sections vertically on small screens */
  background-color: var(
    --mat-card-background
  ); /* Use Angular Material card background color */
  box-shadow: var(--mat-elevation-z4); /* Use Angular Material elevation */
  border-radius: 0.5rem; /* rounded-lg */
  overflow: hidden;
  max-width: 1200px; /* max-w-6xl equivalent, adjust as needed */
  width: 100%;
}

@media (min-width: 768px) {
  /* md breakpoint */
  .login-split-wrapper {
    flex-direction: row; /* Arranges sections horizontally on larger screens */
  }
}

.left-section {
  width: 100%;
  padding: 32px; /* p-8 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center; /* Center items for mobile */
  text-align: center; /* Center text for mobile */
}

@media (min-width: 768px) {
  /* md breakpoint */
  .left-section {
    width: 50%;
    text-align: left; /* Align text left on desktop */
    align-items: flex-start; /* Align items to start on desktop */
  }
}

.logo-area {
  display: flex;
  align-items: center;
  margin-bottom: 24px; /* mb-6 */
}

.logo-svg {
  width: 64px; /* w-16 */
  height: 64px; /* h-16 */
  margin-right: 16px; /* mr-4 */
}

.section-text {
  color: var(--mat-body-text); /* Use Angular Material body text color */
  font-family: Montserrat;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: justify; /* justify text */
  line-height: 1.5; /* improved readability */
}

.right-section {
  width: 100%;
  padding: 32px; /* p-8 */
  background-color: var(
    --mat-background
  ); /* Use Angular Material background color */
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  /* md breakpoint */
  .right-section {
    width: 50%;
  }
}

.login-form-card {
  width: 100%;
  max-width: 448px; /* max-w-md */
  padding: 24px; /* p-6 */
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: var(--mat-elevation-z8); /* Use Angular Material elevation */
}

.card-title {
  font-size: 24px; /* text-2xl */
  margin-bottom: 24px; /* mb-6 */
  margin-left: 1rem;
  color: var(--mat-primary); /* Use Angular Material primary color */
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 10px; /* gap-5 */
}

.full-width-input {
  width: 100%;
}

.remember-me-checkbox {
  margin-bottom: 16px; /* mb-4 */
  color: var(--mat-body-text); /* Use Angular Material body text color */
}

.login-button {
  width: 100%;
  height: 48px; /* h-12 */
  border-radius: 0.375rem; /* rounded-md */
  font-size: 1.125rem; /* text-lg */
  box-shadow: var(--mat-elevation-z4); /* Use Angular Material elevation */
  transition: box-shadow 0.3s ease-in-out; /* transition-shadow duration-300 */
  display: flex; /* For spinner alignment */
  justify-content: center; /* For spinner alignment */
  align-items: center; /* For spinner alignment */
  margin-top: 5rem;
}

.login-button:hover {
  box-shadow: var(--mat-elevation-z8); /* Use Angular Material elevation */
}

/* Material icon sizing */
mat-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
}

/* Spinner color for Material button if needed, otherwise 'color="accent"' handles it */
.mat-progress-spinner::ng-deep circle {
  stroke: currentColor; /* Inherit color from button text */
}

.form-label {
  display: block;
  font-size: 16px;
}

.required {
  color: #ff1b1b;
}
.error-message {
  color: #ff1b1b;
  font-size: 12px;
}
.invalid-credentials {
  color: #ff1b1b;
  font-size: 12px;
  margin-top: -17px;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  box-shadow: 0 0 0 1000px var(--mat-background) inset !important;
  -webkit-box-shadow: 0 0 0 1000px var(--mat-background) inset !important;
  -webkit-text-fill-color: var(--mat-body-text) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}
.version {
  margin-top: 2rem;
}
