import { Component, Inject } from '@angular/core';
import {
  FormBuilder,
  Validators,
  AbstractControl,
  ValidatorFn,
  FormGroup,
  ValidationErrors,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ChangePasswordService } from './change-password.service';
import { SnackbarService } from '../../../shared/services/snackbar.service';
import { AuthService } from '../../../core/services/auth.service';
import { STRONGPASSWORDPATTERN } from '../../../app.constant';
import { Router } from '@angular/router';

@Component({
  selector: 'cnc-change-password',
  templateUrl: './change-password.component.html',
  standalone: false,
  styleUrls: ['./change-password.component.scss'],
})
export class ChangePasswordComponent {
  hideOld = true;
  hideNew = true;
  hideConfirm = true;

  passwordForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ChangePasswordComponent>,
    private changePasswordService: ChangePasswordService,
    private snackbarService: SnackbarService,
    private authService: AuthService,
    private router: Router,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.passwordForm = this.fb.group(
      {
        username: [
          '',
          [
            Validators.required,
            Validators.minLength(3),
            Validators.pattern(/^[a-zA-Z0-9]+$/),
          ],
        ],
        oldPassword: ['', Validators.required],
        newPassword: [
          '',
          [Validators.required, Validators.pattern(STRONGPASSWORDPATTERN)],
        ],
        confirmPassword: [
          '',
          [Validators.required, Validators.pattern(STRONGPASSWORDPATTERN)],
        ],
      },
      { validators: this.passwordValidator }
    );
  }

  passwordValidator(control: AbstractControl): ValidationErrors | null {
    const password = control.get('newPassword')?.value;
    const confirmPassword = control.get('confirmPassword')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      control.get('confirmPassword')?.setErrors({ passwordMismatch: true });
    } else {
      const errors = control.get('confirmPassword')?.errors;
      if (errors && errors['passwordMismatch']) {
        delete errors['passwordMismatch'];
        if (Object.keys(errors).length === 0) {
          control.get('confirmPassword')?.setErrors(null);
        } else {
          control.get('confirmPassword')?.setErrors(errors);
        }
      }
    }

    return null;
  }
  onCancel(): void {
    this.dialogRef.close();
  }
  get f() {
    return this.passwordForm.controls;
  }
  onSubmit(): void {
    if (this.passwordForm.invalid) {
      this.passwordForm.markAllAsTouched();
      return;
    }

    const { username, oldPassword, newPassword } = this.passwordForm.value;

    this.changePasswordService
      .changePassword(oldPassword, newPassword)
      .subscribe({
        next: (res) => {
          this.snackbarService.open(res.message, '', 'success', 3000);

          setTimeout(() => {
            this.authService.logout().subscribe({
              next: (res) => {
                this.dialogRef.close(true);
                this.snackbarService.open(res.message, '', 'success', 3000);

                this.router.navigate(['login']);
              },
              error: () => {
                this.snackbarService.open('Logout failed', '', 'failed', 3000);
                this.dialogRef.close(true);
                this.router.navigate(['login']);
              },
            });
          }, 2000);
        },
        error: (err) => {
          this.snackbarService.open(err.error.message, '', 'failed', 3000);
        },
      });
  }
}
