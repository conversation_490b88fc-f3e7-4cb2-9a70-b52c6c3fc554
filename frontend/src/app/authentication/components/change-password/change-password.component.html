<label class="form-title">{{ data.title }}</label>

<form [formGroup]="passwordForm" class="password-form">
  <!-- Username -->
  <label class="form-label">Username</label>
  <mat-form-field appearance="outline" class="full-width">
    <input
      matInput
      formControlName="username"
      placeholder="Enter your username"
    />
    <mat-icon matPrefix>person</mat-icon>
    <mat-error *ngIf="passwordForm.get('username')?.errors as errors">
      <ng-container *ngIf="errors['required']">
        Username is required
      </ng-container>
      <ng-container *ngIf="!errors['required'] && errors['minlength']">
        Username must be at least 3 characters
      </ng-container>
      <ng-container
        *ngIf="!errors['required'] && !errors['minlength'] && errors['pattern']"
      >
        Username must be alphanumeric
      </ng-container>
    </mat-error>
  </mat-form-field>

  <!-- Old Password -->
  <label class="form-label">Enter old password</label>
  <mat-form-field appearance="outline" class="full-width">
    <input
      matInput
      [type]="hideOld ? 'password' : 'text'"
      formControlName="oldPassword"
    />
    <button
      mat-icon-button
      matSuffix
      type="button"
      (click)="hideOld = !hideOld"
    >
      <mat-icon>{{ hideOld ? 'visibility_off' : 'visibility' }}</mat-icon>
    </button>
    <mat-icon matPrefix>lock</mat-icon>
    <mat-error *ngIf="passwordForm.get('oldPassword')?.hasError('required')"
      >Old password is required</mat-error
    >
  </mat-form-field>

  <!-- New Password -->
  <label class="form-label">Enter new password</label>
  <mat-form-field appearance="outline" class="full-width">
    <input
      matInput
      [type]="hideNew ? 'password' : 'text'"
      formControlName="newPassword"
    />
    <button
      mat-icon-button
      matSuffix
      type="button"
      (click)="hideNew = !hideNew"
    >
      <mat-icon>{{ hideNew ? 'visibility_off' : 'visibility' }}</mat-icon>
    </button>
    <mat-icon matPrefix>lock</mat-icon>
    <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('required')"
      >New password is required</mat-error
    >
    <mat-error
      style="margin-bottom: 0.2rem"
      *ngIf="
        passwordForm.get('newPassword')?.hasError('pattern') &&
        passwordForm.get('newPassword')?.touched
      "
    >
      Password must be 8–16 characters with 1 uppercase, 1 lowercase, 1 number,
      and 1 special character.</mat-error
    >
  </mat-form-field>

  <!-- Confirm Password -->
  <label class="form-label" style="margin-top: 0.5rem">Confirm password</label>
  <mat-form-field appearance="outline" class="full-width">
    <input
      matInput
      [type]="hideConfirm ? 'password' : 'text'"
      formControlName="confirmPassword"
    />
    <button
      mat-icon-button
      matSuffix
      type="button"
      (click)="hideConfirm = !hideConfirm"
    >
      <mat-icon>{{ hideConfirm ? 'visibility_off' : 'visibility' }}</mat-icon>
    </button>
    <mat-icon matPrefix>lock</mat-icon>
    <mat-error
      *ngIf="passwordForm.get('confirmPassword')?.hasError('required')"
    >
      Confirm password is required
    </mat-error>
    <mat-error
      *ngIf="
        f['confirmPassword'].touched &&
        f['confirmPassword'].errors?.['passwordMismatch']
      "
    >
      Confirm password does not match.
    </mat-error>
  </mat-form-field>

  <!-- Actions -->
  <div class="form-actions">
    <button
      mat-stroked-button
      color="primary"
      class="action-button"
      (click)="onCancel()"
    >
      Cancel
    </button>
    <button
      mat-flat-button
      color="primary"
      class="action-button"
      [disabled]="!passwordForm.valid"
      (click)="onSubmit()"
    >
      Update
    </button>
  </div>
</form>
