import { Injectable } from '@angular/core';
import { AuthService } from '../../../core/services/auth.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ChangePasswordRequest } from './changePassword.modal';
import { userUrl } from '../../../app.constant';

@Injectable({
  providedIn: 'root',
})
export class ChangePasswordService {
  constructor(
    private authService: AuthService,
    private http: HttpClient
  ) {}
  changePassword(oldPassword: string, newPassword: string): Observable<any> {
    const user = this.authService.currentUser;
    console.log(user, 'sh');
    if (!user) {
      throw new Error('User is not logged in');
    }

    const payload: ChangePasswordRequest = {
      id: user.userId,
      username: user.username,
      oldPassword,
      newPassword,
    };

    return this.http.post(`${userUrl}change-password`, payload);
  }
}
