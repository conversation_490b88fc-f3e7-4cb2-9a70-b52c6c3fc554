const ip = '*********';
const port = 3001;
const version = 'v1';
// Uncomment baseUrl and authUrl for local testing
// const baseUrl = `http://${ip}:${port}/api/${version}/`;
// // export const authUrl = `/api/${version}/auth/`;

// Comment baseUrl and authUrl for local testing
const baseUrl = `http://localhost/api/`;
export const authUrl = `${baseUrl}auth/`;

export const shiftUrl = `${baseUrl}shift/`;
export const departmenturl = `${baseUrl}department/`;
export const settingsUrl = `${baseUrl}setting/`;
export const userUrl = `${baseUrl}user/`;

//  export const imageBasePath = 'http://*********:3001';
export const imageBasePath = 'http://localhost/';

export const STRONGPASSWORDPATTERN =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,16}$/;

export const THEME_COLORS = [
  { id: 'red-theme', value: '#D90707' },
  { id: 'blue-theme', value: '#096FC9' },
  { id: 'yellow-theme', value: '#CDBD12' },
  { id: 'purple-theme', value: '#AA07C3' },
  { id: 'green-theme', value: '#2BAC0A' },
  { id: 'orange-theme', value: '#D29803' },
];

export const DATE_MONTH_NAME_FORMAT = {
  parse: {
    dateInput: 'LL',
  },
  display: {
    dateInput: 'LL',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

export const USER_ROLES = {
  SUPERADMIN: 'superadmin',
  ADMIN: 'admin',
  USERROLE1: 'userrole1',
  USERROLE2: 'userrole2',
};

export const USER_ROLES_LIST = {
  [USER_ROLES.SUPERADMIN]: 'Super Admin',
  [USER_ROLES.ADMIN]: 'Admin',
  [USER_ROLES.USERROLE1]: 'User Role 1',
  [USER_ROLES.USERROLE2]: 'User Role 2',
};
