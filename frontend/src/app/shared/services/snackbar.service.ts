import { inject, Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root',
})
export class SnackbarService {
  private _snackBar = inject(MatSnackBar);

  open(message: string, action: string, type: string, time = 300000): void {
    this._snackBar.open(message, action, {
      duration: time,
      horizontalPosition: 'right',
      verticalPosition: 'top',
      panelClass: `${type}-snk`,
    });
  }
}
