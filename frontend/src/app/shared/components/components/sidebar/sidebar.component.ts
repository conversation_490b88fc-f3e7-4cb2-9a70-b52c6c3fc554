import { Component, inject, Input, OnInit } from '@angular/core';
import {
  ADMIN_MENU,
  SUPERADMIN_MENU,
  USER_ROLE_1_MENU,
  USER_ROLE_2_MENU,
} from './menus.constants';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'cnc-sidebar',
  standalone: false,
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent implements OnInit {
  @Input() collapsed = true;
  menuItems = SUPERADMIN_MENU;
  currentUserRole = inject(AuthService).currentUser?.role;

  ngOnInit(): void {
    this.updateMenuItems();
  }

  updateMenuItems(): void {
    switch (this.currentUserRole) {
      case 'userrole1':
        this.menuItems = USER_ROLE_1_MENU;
        break;
      case 'userrole2':
        this.menuItems = USER_ROLE_2_MENU;
        break;
      case 'admin':
        this.menuItems = ADMIN_MENU;
        break;
      default:
        this.menuItems = SUPERADMIN_MENU;
    }
  }
}
