.sidebar {
  height: 100vh;
  position: fixed;
  border-right: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  margin-top: 6px;
  padding-top: 60px; // account for toolbar
  transition: width 0.3s ease;
  width: 300px;
  &.collapsed {
    width: 70px;

    .sidebar-item {
      border-radius: 5px;
      justify-content: center;

      span {
        display: none;
      }
    }
  }

  .sidebar-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    color: var(--mat-body-text);
    text-decoration: none;
    transition: background-color 0.2s ease;
    border-radius: 5px;

    mat-icon {
      margin-right: 12px;
    }
    &.active {
      background-color: var(--mat-sys-primary);
      color: white;
    }
  }
}
.sidebar-item.active .sidebar-icon {
  filter: brightness(0) invert(1);
}

.itemLabel {
  margin-left: 1rem;
  border-radius: 5px;
}
.sidebar-icon {
  width: 25px;
  &.collapsed {
    margin-right: 2rem;
  }
}
.sidebar-item.collapsed {
  width: 28px;
  padding: 6px !important;
  margin-left: 9px;
}
.sidebar-item {
  margin-bottom: 10px;
}
.outer-container {
  padding: 5px;
}
