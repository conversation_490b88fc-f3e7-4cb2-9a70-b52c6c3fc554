<div class="sidebar" [ngClass]="{ collapsed: collapsed }">
  <div class="outer-container">
    <a
      class="sidebar-item"
      *ngFor="let item of menuItems"
      matTooltip="{{ collapsed ? item.label : '' }}"
      matTooltipPosition="right"
      [ngClass]="{ collapsed: collapsed }"
      routerLinkActive="active"
      [routerLink]="item.route"
    >
      <img
        [src]="item.icon"
        class="sidebar-icon"
        *ngIf="item.icon"
        alt="{{ item.label }}"
      />
      <span *ngIf="!collapsed" class="itemLabel">{{ item.label }}</span>
    </a>
  </div>
</div>
