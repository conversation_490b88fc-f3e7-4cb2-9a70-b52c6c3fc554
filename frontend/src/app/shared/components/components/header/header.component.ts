import { Component, EventEmitter, Output } from '@angular/core';
import { SettingsServiceGloabl } from '../../../../core/services/settings.service';
import { AuthService } from '../../../../core/services/auth.service';
import { ConfirmDialogComponent } from '../../confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { SnackbarService } from '../../../services/snackbar.service';
import { ChangePasswordComponent } from '../../../../authentication/components/change-password/change-password.component';
import { SettingsService } from '../../../../main-app/modules/settings/settings.service';

@Component({
  selector: 'cnc-header',
  standalone: false,
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent {
  @Output() toggleMenu = new EventEmitter<void>();
  isDarkMode = false;
  loader = true;
  fullScreenMode = false;
  constructor(
    private settingsServiceGloabl: SettingsServiceGloabl,
    private authService: AuthService,
    private dialog: MatDialog,
    private router: Router,
    private snackbarService: SnackbarService,
    public settingsService: SettingsService
  ) {
    // Initialize the theme based on the stored mode
    this.isDarkMode = this.settingsServiceGloabl.getMode() === 'dark';
    this.toggleTheme();

    this.settingsService.fullScreenMode$.subscribe(
      (mode) => (this.fullScreenMode = mode)
    );
  }
  toggleTheme(): void {
    const htmlElement = document.documentElement;
    if (this.isDarkMode) {
      htmlElement.classList.add('dark-mode');
      htmlElement.classList.remove('light-mode');
    } else {
      htmlElement.classList.add('light-mode');
      htmlElement.classList.remove('dark-mode');
    }
    this.settingsServiceGloabl.setMode(this.isDarkMode ? 'dark' : 'light');
  }

  toggleFullScreen() {
    this.fullScreenMode = !this.fullScreenMode;
    this.settingsService.setFullScreenMode(this.fullScreenMode);
  }

  logout(): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '400px',
      data: {
        title: 'Logout',
        message: `Are you sure you want to log out from the application?`,
        cancelButtonName: 'Cancel',
        okButtonName: 'Logout',
      },
      panelClass: 'alert-dialog',
    });

    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.loader = true;

        const token = localStorage.getItem('token');
        if (!token) {
          this.finalizeLogout();
          return;
        }

        this.authService.logout().subscribe({
          next: (res) => {
            this.snackbarService.open(res.message, '', 'success', 3000);

            this.finalizeLogout();
          },
          error: () => {
            this.snackbarService.open('Logout failed', '', 'failed', 3000);
            this.finalizeLogout();
          },
        });
      }
    });
  }
  finalizeLogout(): void {
    document.body.classList.remove('dark-mode');
    document.documentElement.classList.remove('dark-mode');
    this.loader = false;
    this.router.navigate(['login']);
  }
  OnChangePassword() {
    const dialogRef = this.dialog.open(ChangePasswordComponent, {
      maxWidth: '800px',
      width: '100%',
      panelClass: 'alert-dialog',
      data: {
        title: 'Change Password',

        source: {
          id: 0,
          name: '',
          logo: '',
        },
      },
    });

    // dialogRef.afterClosed().subscribe((result: IDepartmentDialogResult) => {
    //   if (!result) return;
    //   const { action, data } = result;
    //   if (action === 'save') {
    //     this.addDepartment(data);
    //   }
    // });
  }
  Profile() {
    this.router.navigate(['/app/profile']);
  }
}
