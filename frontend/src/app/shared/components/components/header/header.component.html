<mat-toolbar class="toolbar" color="primary">
  <button mat-icon-button (click)="toggleMenu.emit()" class="menu-button">
    <!-- <img src="/SideBarIcons/material-symbols_menu.svg" class="menu-icon" /> -->
    <mat-icon>menu</mat-icon>
  </button>
  <img src="icons/shopcnc.svg" class="logo" alt="Shop CNC Logo" />
  <div class="vl"></div>
  @if (settingsService.logoImg()) {
    <img
      [src]="settingsService.logoImg()"
      class="logo-brand"
      alt="Brand Logo"
    />
  }
  <span class="spacer"></span>

  <ng-container *ngIf="!fullScreenMode">
    <!-- inside your toolbar -->
    <div class="theme-switcher">
      <label class="theme-label" for="light-theme">Light</label>
      <label class="switch">
        <input
          type="checkbox"
          [(ngModel)]="isDarkMode"
          (change)="toggleTheme()"
        />
        <span class="slider"></span>
      </label>
      <label class="theme-label" for="dark-theme">Dark</label>
    </div>

    <!-- Notification Icon -->
    <div class="toolbar-icons">
      <button mat-icon-button>
        <img
          src="/SideBarIcons/NotificationBell.svg"
          alt="NotificationBell"
          class="toolbar-icon"
        />
      </button>

      <button mat-icon-button [matMenuTriggerFor]="menu">
        <img src="/SideBarIcons/Profile.svg" alt="User" class="toolbar-icon" />
      </button>

      <mat-menu #menu="matMenu">
        <div class="dropmenu-user">
          <li>
            <a class="profile-link" (click)="Profile()">View Profile</a>
          </li>

          <li>
            <a class="profile-link" (click)="OnChangePassword()"
              >Change Password</a
            >
          </li>
          <li>
            <a class="profile-link" (click)="logout()">Logout</a>
          </li>
        </div>
      </mat-menu>
    </div>
    <!-- Theme Switcher -->
  </ng-container>

  <ng-container *ngIf="fullScreenMode">
    <div class="theme-switcher">
      <label class="theme-label">Light</label>
      <label class="switch">
        <input
          type="checkbox"
          [(ngModel)]="isDarkMode"
          (change)="toggleTheme()"
        />
        <span class="slider"></span>
      </label>
      <label class="theme-label">Dark</label>
    </div>

    <div class="fullscreen-btn">
      <img
        (click)="toggleFullScreen()"
        src="icons/fullscreen-view.svg"
        alt=""
      />
    </div>
  </ng-container>
</mat-toolbar>
