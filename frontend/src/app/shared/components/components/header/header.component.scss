.toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .menu-button {
    margin-right: 20px;

    mat-icon {
      font-size: 24px;
      color: var(--mat-sys-primary);
    }
  }
  .vl {
    border-left: 1px solid black;
    height: 20px;
    margin-left: 1rem;
  }
  .logo {
    height: 37px;
  }
  .title {
    font-size: 20px;
    font-weight: 500;
  }

  .spacer {
    flex: 1 1 auto;
  }
}
.menu-icon {
  width: 24px;
  height: 24px;
}
.theme-switcher {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px;
  margin-right: 4rem;
}

.theme-label {
  font-size: 16px;
  color: var(--mat-body-text);
}
.toolbar-icons {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-right: 8px;
}
.toolbar-icon {
  width: 30px;
  height: 30px;
}
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 15px;
  margin: 0px 10px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 20px;
}

.slider::before {
  position: absolute;
  content: '';
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--mat-sys-primary);
}

input:checked + .slider::before {
  transform: translateX(20px);
}

.dropmenu-user {
  padding: 10px;
  margin-bottom: -7px;
  background-color: var(--mat-sys-on-primary);
}

.dropmenu-user li {
  list-style: none;
  display: block;
  padding: 15px;
}
.dropmenu-user li a {
  font-size: 18px;
  cursor: pointer;
}
.logo-brand {
  height: 60px;
  width: 150px;
}

.fullscreen-btn img {
  width: 33px;
  margin-right: 25px;
  cursor: pointer;
  margin-top: 8px;
}
