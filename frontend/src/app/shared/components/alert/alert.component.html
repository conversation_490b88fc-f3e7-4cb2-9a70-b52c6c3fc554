<label>
  {{ title }}
</label>

<div class="modal-content">
  <mat-icon *ngIf="data.icon">{{ data.icon }}</mat-icon>
  <p [innerHtml]="message" style="text-align: center"></p>
  <hr />
</div>
<div mat-dialog-actions class="modal-action">
  <button
    mat-stroked-button
    class="action-button"
    color="primary"
    (click)="onDismiss()"
    tabindex="-1"
  >
    {{ data.buttonName }}
  </button>
</div>
