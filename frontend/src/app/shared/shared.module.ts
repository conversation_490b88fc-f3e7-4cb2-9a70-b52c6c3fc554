import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreModule } from '../core/core.module';
import { LoaderComponent } from './components/loader/loader.component';
import { SidebarComponent } from './components/components/sidebar/sidebar.component';
import { HeaderComponent } from './components/components/header/header.component';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';
import { AlertComponent } from './components/alert/alert.component';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
@NgModule({
  declarations: [
    LoaderComponent,
    SidebarComponent,
    HeaderComponent,
    AlertComponent,
    ConfirmDialogComponent,
  ],
  imports: [
    CommonModule,
    CoreModule,
    RouterModule,
    MatToolbarModule,
    MatIconModule,
    MatTooltipModule,
    MatSlideToggleModule,
  ],
  exports: [LoaderComponent, SidebarComponent, HeaderComponent],
})
export class SharedModule {}
