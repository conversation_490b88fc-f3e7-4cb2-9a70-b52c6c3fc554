{"name": "my-shop-cnc", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "start:proxy": "ng serve --proxy-config=src/proxy.cloud.conf.json", "watch": "ng build --watch --configuration development", "test": "ng test", "format:core": "prettier \"**/*.{ts,js,html,scss}\"", "format": "npm run format:core -- --write", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.2.0", "@angular/cdk": "^19.2.18", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.18", "@angular/material-moment-adapter": "^19.2.18", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "chart.js": "^3.9.1", "d3": "^7.9.0", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "ng2-charts": "^4.0.0", "ngx-file-drop": "^16.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.14", "@angular/cli": "^19.2.14", "@angular/compiler-cli": "^19.2.0", "@types/d3": "^7.4.3", "@types/jasmine": "~5.1.0", "angular-eslint": "20.1.1", "eslint": "^9.29.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "8.34.1"}}