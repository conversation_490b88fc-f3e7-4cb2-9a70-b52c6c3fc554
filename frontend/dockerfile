# Stage 1: Build Angular app
FROM node:18-alpine as build

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the files and build
COPY . .
RUN npm run build

# Stage 2: Serve with NGINX
FROM nginx:alpine

# Copy built Angular app
COPY --from=build /app/dist/myshopcnc/browser/ /usr/share/nginx/html

# Copy custom NGINX config if present
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
