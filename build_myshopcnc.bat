@echo off
setlocal
echo ==========================
echo FRONTEND BUILD PROCESS
echo ==========================

:: Step 1: Delete old build
echo Checking for existing frontend build folder...
IF EXIST "frontend\dist\myshopcnc\browser" (
    echo Deleting existing frontend build folder...
    rmdir /s /q "frontend\dist\myshopcnc\browser"
)

:: Step 2: Build frontend
echo Changing to frontend directory...
cd frontend || (
    echo Failed to change directory to frontend
    pause
    goto :eof
)

echo Installing packages (suppressing audit and funding messages)...
call npm install --no-fund --no-audit
IF ERRORLEVEL 1 (
    echo npm install failed.
    pause
    goto :eof
)
echo Finished npm install step.

echo Running build...
call npm run build
IF ERRORLEVEL 1 (
    echo npm run build failed.
    pause
    goto :eof
)
echo Finished npm run build.

echo Build complete, returning to root folder...
cd .. || (
    echo Failed to return to root folder
    pause
    goto :eof
)
echo Returned to root folder successfully.

echo Current directory:
cd
timeout /t 2 /nobreak > nul
:: sleep 2 seconds to ensure build is complete
:: Root folder path
echo ==========================
echo DOCKER BUILD + IMAGE SAVE
echo ==========================

:: Step 3: Build Docker containers
echo Building Docker containers...
docker compose up --build -d
IF ERRORLEVEL 1 (
    echo Docker compose build failed.
    pause
    goto :eof
)

echo Starting containers...
docker compose up -d
IF ERRORLEVEL 1 (
    echo Docker compose up failed.
    pause
    goto :eof
)
echo Docker containers started.

:: Step 4: Save Docker images
echo Saving Docker images...
if not exist "docker-images" (
    mkdir docker-images
)

echo Saving userservice image...
docker save -o docker-images/userservice.tar myshopcnc-userservice:latest
IF ERRORLEVEL 1 (
    echo Failed to save userservice image.
    pause
    goto :eof
)

:: echo Saving frontend image...
:: docker save -o docker-images/frontend.tar myshopcnc-frontend:latest

echo Saving nginx image...
docker save -o docker-images/nginx.tar myshopcnc-nginx:latest
IF ERRORLEVEL 1 (
    echo Failed to save nginx image.
    pause
    goto :eof
)

echo Saving authservice image...
docker save -o docker-images/authservice.tar myshopcnc-authservice:latest
IF ERRORLEVEL 1 (
    echo Failed to save authservice image.
    pause
    goto :eof
)

echo Saving database image...
docker save -o docker-images/db.tar timescale/timescaledb:latest-pg15
IF ERRORLEVEL 1 (
    echo Failed to save database image.
    pause
    goto :eof
)

echo ==========================
echo DONE.
echo ==========================
pause
echo Script completed. Press any key to exit.
