# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/
frontend/node_modules/
backend/*/node_modules/

# Build outputs
dist/
frontend/dist/

# Environment variables
.env
frontend/.env
backend/*/.env

# OS-specific
.DS_Store
Thumbs.db

# Docker
*.pid
docker-compose.override.yml
docker-images

# IDEs and editors
.vscode/
.idea/
*.sw?

# TypeScript
*.tsbuildinfo

# Angular CLI
.angular/
.angular-cache/
frontend/.angular/

# Test coverage
coverage/
.nyc_output/

# Misc
*.tgz
*.gz
*.zip
