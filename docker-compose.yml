services:
#   frontend:
#     build: ./frontend
#     container_name: frontend
#     # env_file:
#     #   - ./frontend/.env
#     ports:
#       - '8080:80'
#     depends_on:
#       - nginx
#     networks:
#       - myshopcnc-net

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: timescale
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: root@123
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./timescaledb/init.sql:/docker-entrypoint-initdb.d/init.sql
  #  ports:
  #    - '5432:5432'
    networks:
      - myshopcnc-net

  authservice:
    image: myshopcnc-authservice:latest
    build:
      context: ./backend/authservice
      dockerfile: dockerfile
    container_name: authservice
    env_file:
      - ./backend/authservice/.env
    environment:
    - DB_HOST=timescaledb
    - TZ=Asia/Kolkata
    depends_on:
      - timescaledb
    networks:
      - myshopcnc-net
    restart: always

  userservice:
    image: myshopcnc-userservice:latest
    build:
      context: ./backend/userservice
      dockerfile: dockerfile
    container_name: userservice
    env_file:
      - ./backend/userservice/.env
    environment:
    - DB_HOST=timescaledb
    - TZ=Asia/Kolkata
    depends_on:
      - timescaledb
    networks:
      - myshopcnc-net
    volumes:
      - logo_data:/app/images
    restart: always
    
  nginx:
    image: myshopcnc-nginx:latest
    build: ./nginx
    container_name: nginx-proxy
    ports:
      - '80:80'
    depends_on:
      - userservice
      - authservice
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./frontend/dist/myshopcnc/browser:/var/www/html
      - logo_data:/app/images
    networks:
      - myshopcnc-net

volumes:
  timescale_data:
  logo_data:

networks:
  myshopcnc-net:
