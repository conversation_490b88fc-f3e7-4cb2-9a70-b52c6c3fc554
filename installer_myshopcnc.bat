@echo off
setlocal EnableDelayedExpansion

:: Set global image version
set IMAGE_VERSION=v0.0.4

echo [%TIME%] Checking Docker...
docker info >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo Docker is not running. Please install and start Docker Desktop.
    pause
    exit /b
)

echo.
echo [%TIME%] Loading pre-downloaded Docker images...

CALL :loadImage "userservice" "myshopcnc-userservice" "docker-images\userservice.tar"
:: Frontend image is intentionally skipped (commented out)
:: CALL :loadImage "frontend" "myshopcnc-frontend" "docker-images\frontend.tar"
CALL :loadImage "nginx" "myshopcnc-nginx" "docker-images\nginx.tar"
CALL :loadImage "authservice" "myshopcnc-authservice" "docker-images\authservice.tar"
CALL :loadImage "db" "timescale/timescaledb" "docker-images\db.tar"

echo.
echo [%TIME%] Stopping existing containers (volumes will be preserved)...
docker compose down

echo.
echo [%TIME%] Starting containers...
docker compose up -d

timeout /t 5 >nul

echo.
echo Verifying running containers...
docker ps

:: Skipping frontend container check since it's commented out in compose
:: Get current IP
FOR /F "tokens=2 delims=:" %%A IN ('ipconfig ^| findstr /C:"IPv4 Address"') DO (
    SET IP=%%A
    GOTO :foundIP
)
:foundIP
SET IP=%IP:~1%

echo.
echo Opening NGINX proxy in browser: http://%IP%:80
start http://%IP%:80

pause
exit /b

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:loadImage
:: %1 - logical name
:: %2 - image name
:: %3 - tar file
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

SET NAME=%~1
SET IMAGE=%~2
SET FILE=%~3
SET VERSIONED_IMAGE=%IMAGE%:%IMAGE_VERSION%
SET LATEST_IMAGE=%IMAGE%:latest
SET BACKUP_TAG=%IMAGE%:backup-%RANDOM%

:: Check if 'latest' tag exists and backup if so
docker image inspect %LATEST_IMAGE% >nul 2>&1
IF %ERRORLEVEL% EQU 0 (
    echo [%TIME%] Found existing 'latest' image for %NAME%. Creating backup tag %BACKUP_TAG%...
    docker tag %LATEST_IMAGE% %BACKUP_TAG%
)

:: Load and tag new image
IF EXIST %FILE% (
    echo [%TIME%] Loading new image for %NAME% from %FILE%...
    FOR /F "tokens=3" %%I IN ('docker load -i %FILE% ^| findstr "Loaded image:"') DO (
        SET LOADED_IMAGE=%%I
    )

    echo [%TIME%] Tagging loaded image as %VERSIONED_IMAGE% and %LATEST_IMAGE%...
    docker tag !LOADED_IMAGE! %VERSIONED_IMAGE%
    docker tag !LOADED_IMAGE! %LATEST_IMAGE%
) ELSE (
    echo [%TIME%] ⚠️ [Warning] %NAME% tar not found at %FILE%
)

goto :eof
