import axios from 'axios';

export const blockUserTokens = async (userId, token) => {
    if (!userId && !token) {
        throw new Error('No userId or token provided');
    }
    try {
        // Uncomment below code for local testing    
        // const authUrl = `http://${process.env.AUTH_IP}:${process.env.AUTH_PORT}` || 'http://localhost:3000';
        // await axios.post(authUrl + process.env.BASE_ROUT + 'auth/block-token', {
        //     userId,
        //     token
        // });

        // Comment below code for local testing    
        const authUrl = `${process.env.NGINX_PROXY}api/` || `http://nginx-proxy/api/`;
        await axios.post(authUrl + 'auth/block-token', {
            userId,
            token
        });
    } catch (error) {
        throw error;
    }
};
