import axios from 'axios';
import responseFormatter from './responseFormator.js';
import StatusCodes from '../config/statusCodes.js';

const formatter = new responseFormatter();

export const validateAuthToken = async (req, res, next) => {
    const token = req.headers.authorization;

    if (!token) {
        return formatter.error('No token provided', null, res, StatusCodes.BAD_REQUEST);
    }

    try {
        // Uncomment below code for local testing    
        // const authUrl = `http://${process.env.AUTH_IP}:${process.env.AUTH_PORT}` || 'http://*********:3000';
        // const response = await axios.get(authUrl + process.env.BASE_ROUT + 'auth/validate-token', {
        //         headers: { Authorization: token }
        //     });

        // Comment below code for local testing    
        const authUrl = `${process.env.NGINX_PROXY}api/` || `http://nginx-proxy/api/`
        const response = await axios.get(authUrl + 'auth/validate-token', {
            headers: { Authorization: token }
        });

        if (response.data && response.data.status) {
            req.user = response.data.data[0].user;
            next();
        } else {
            return formatter.error('Invalid token response', null, res, StatusCodes.FORBIDDEN);
        }
    } catch (error) {
        const statusCode = error.response?.status || StatusCodes.FORBIDDEN;
        const message = statusCode === StatusCodes.UNAUTHORIZED 
            ? 'Token expired or invalid' 
            : 'Token validation failed';

        return formatter.error(message, error, res, statusCode);
    }
};