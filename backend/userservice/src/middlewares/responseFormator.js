var responseFormatter = function () { }

let headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST,DELETE,GET,PUT",
    "Content-Type": "application/json"
}

async function allowCrossDomain(res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

responseFormatter.prototype.success = async function (results, errors = null, res, msg) {
    let records = 0;
    if (results == undefined || results == null) {
        results = [];
    }
    if (results && !Array.isArray(results))
        results = [results];

    if (results && results.length && results[0].hasOwnProperty("full_count")) {
        records = results[0]["full_count"];
        results.forEach(function (result) { delete result["full_count"] });
    }
    await allowCrossDomain(res);

    let responseObj = {
        body:
        {
            status: true,
            data: results ? results : [],
            error_code: null,
            errors: errors ? errors : [],
            message: (msg && msg.length) ? msg : 'success'
        }
    }
    res.send(responseObj.body)
}

responseFormatter.prototype.html = function (results, errors = null) {
    let records = 0;
    if (results == undefined || results == null) {
        results = [];
    }
    headers["Content-Type"] = "text/html";
    return {
        statusCode: 200,
        headers: headers,
        body: results
    }
}

responseFormatter.prototype.error = function (message, errors = null, res, status_code=500) {
    allowCrossDomain(res);
    let responseObj = {
        body: {
            status: false,
            error_code: status_code,
            data: errors ? errors : [],
            message: message
        }
    }
    res.status(status_code).send(responseObj.body);
}

responseFormatter.prototype.unAuthorize = function (message, res) {
    allowCrossDomain(res);
    let responseObj = {
        body:
        {
            status: false,
            error_code: 403,
            data: [],
            message: message
        }
    }
    res.send(responseObj.body);
}

export default responseFormatter;