const getListQueryForSuperAdmin = `SELECT * FROM shifts;`;

const getListQueryForAdmin = `SELECT * FROM shifts WHERE id = ANY (SELECT unnest(shifts)::uuid FROM users WHERE id = $1);`;

const createQuery = `INSERT INTO shifts (shift_name, time_format, start_time, end_time, break_time, departments) VALUES ($1,$2,$3,$4,$5,$6) RETURNING *;`;

const updateQuery = `UPDATE shifts SET shift_name = $1, time_format = $2, start_time = $3, end_time = $4, break_time = $5, departments = $6 WHERE id = $7 RETURNING *`;

const deleteQuery = `DELETE FROM shifts WHERE id = $1 RETURNING *;`;

const shiftExistsQuery = `SELECT id FROM shifts WHERE shift_name = $1`;

const shiftTimeOverlapCreateQuery = `SELECT id FROM shifts WHERE (TIMESTAMP '2000-01-01' + start_time::time, TIMESTAMP '2000-01-01' + end_time::time) OVERLAPS (TIMESTAMP '2000-01-01' + $1::time, TIMESTAMP '2000-01-01' + $2::time);`;

const shiftTimeOverlapUpdateQuery = `SELECT id FROM shifts WHERE id != $1 AND (TIMESTAMP '2000-01-01' + start_time::time, TIMESTAMP '2000-01-01' + end_time::time) OVERLAPS (TIMESTAMP '2000-01-01' + $2::time, TIMESTAMP '2000-01-01' + $3::time);`;

const userCheckQuery = 'SELECT 1 FROM users WHERE $1 = ANY(shifts) AND is_deleted = false LIMIT 1;';

export default { getListQueryForSuperAdmin, getListQueryForAdmin, createQuery, updateQuery, deleteQuery, shiftExistsQuery, userCheckQuery, shiftTimeOverlapCreateQuery, shiftTimeOverlapUpdateQuery };