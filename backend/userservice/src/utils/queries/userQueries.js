const getDepartmentsByUserIdQuery = `SELECT departments FROM users WHERE id = $1`;
const getFilteredListQuery = `
  SELECT 
    id,
    username,
    firstname,
    lastname,
    email,
    phone,
    role,
    departments,
    shifts,
    created_at,
    updated_at
  FROM users
  WHERE is_deleted = FALSE
    AND role != 'superadmin'
    AND ($1::text[] IS NULL OR role = ANY($1::text[]))
    AND ($2::uuid[] IS NULL OR departments && $2::uuid[])
    AND (
      $3::text IS NULL OR (
        username ILIKE '%' || $3::text || '%' OR
        firstname ILIKE '%' || $3::text || '%' OR
        lastname ILIKE '%' || $3::text || '%' OR
        email ILIKE '%' || $3::text || '%' OR
        phone ILIKE '%' || $3::text || '%'
      )
    )
  /*ORDER_BY*/
  LIMIT $4::int OFFSET $5::int;
`;

const getFilteredUsersCountQuery = `
  SELECT COUNT(*) AS total
  FROM users
  WHERE is_deleted = FALSE
    AND role != 'superadmin'
    AND ($1::text[] IS NULL OR role = ANY($1::text[]))
    AND ($2::uuid[] IS NULL OR departments && $2::uuid[])
    AND (
      $3::text IS NULL OR (
        username ILIKE '%' || $3::text || '%' OR
        firstname ILIKE '%' || $3::text || '%' OR
        lastname ILIKE '%' || $3::text || '%' OR
        email ILIKE '%' || $3::text || '%' OR
        phone ILIKE '%' || $3::text || '%'
      )
    );
`;

const getProfileQuery = `
  SELECT 
    id,
    username,
    firstname,
    lastname,
    email,
    phone,
    role,
    departments,
    shifts
  FROM users
  WHERE id = $1;
`;

const createQuery = `
  INSERT INTO users (
    username, password, firstname, lastname, email, phone,
    role, departments, shifts, theme, color_scheme, must_reset_password
  )
  VALUES (
    $1, $2, $3, $4, $5, $6, $7,
    $8, $9, $10, $11, TRUE
  )
  RETURNING *;
`;

const updateQuery = `
  UPDATE users SET
    firstname = $1,
    lastname = $2,
    phone = $3,
    departments = $4,
    shifts = $5,
    updated_at = NOW()
  WHERE id = $6
  RETURNING *;
`;

const deleteQuery = `
  UPDATE users
  SET is_deleted = TRUE, is_active = FALSE, updated_at = NOW()
  WHERE id = $1
  RETURNING *;
`;

const getUserByIdAndUsernameQuery = `SELECT * FROM users WHERE id = $1 AND username = $2 AND is_deleted = FALSE;`;

const updatePasswordQuery = `UPDATE users SET password = $1, must_reset_password = FALSE, updated_at = NOW() WHERE id = $2 RETURNING id, username;`;


export default {
  getFilteredListQuery,
  getFilteredUsersCountQuery,
  getProfileQuery,
  createQuery,
  updateQuery,
  deleteQuery,
  getUserByIdAndUsernameQuery,
  updatePasswordQuery,
  getDepartmentsByUserIdQuery
};
