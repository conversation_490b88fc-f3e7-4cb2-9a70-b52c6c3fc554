const getListQuery = `SELECT * FROM departments;`;

const createQuery = `INSERT INTO departments (id, department_name, logo_path, mimetype, department_color) VALUES ($1, $2, $3, $4, $5) RETURNING *;`;

const updateQuery = `UPDATE departments SET department_name = $1, logo_path = $2, mimetype = $3 WHERE id = $4 RETURNING *;`;

const deleteQuery = `DELETE FROM departments WHERE id = $1 RETURNING *;`;

const getDepartmentByIdQuery = `SELECT * FROM departments WHERE id = $1`;

const departmentExistsQuery = `SELECT id FROM departments WHERE department_name = $1`;

const userCheckQuery = 'SELECT 1 FROM users WHERE $1 = ANY(departments) AND is_deleted = false LIMIT 1;';

const getAdminListQuery = `SELECT id, firstname, lastname, email, departments, shifts FROM users WHERE role = 'admin' AND is_active = true`

const getLoginUsersDetails = `SELECT id, firstname, lastname, email, departments, shifts FROM users WHERE id = $1 AND is_active = true`

const getShiftListQuery = `SELECT * FROM shifts;`;

const getUserCountQuery = `SELECT COUNT(*) AS count FROM users WHERE $1 = ANY(shifts) AND $2 = ANY(departments) AND is_active = true AND is_deleted = false AND role != 'superadmin'`;

const getDepartmentByAdminIdQuery = `SELECT * FROM departments WHERE id = ANY($1)`;

export default {
    getListQuery, createQuery, updateQuery, deleteQuery, getDepartmentByIdQuery, departmentExistsQuery,
    getAdminListQuery, userCheckQuery, getLoginUsersDetails, getShiftListQuery, getUserCountQuery, getDepartmentByAdminIdQuery
}