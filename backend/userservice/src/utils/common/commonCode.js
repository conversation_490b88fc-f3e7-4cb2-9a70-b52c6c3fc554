import fs from 'fs';
import path from 'path';
const uploadPath = '/app/images';

/** Utility: Ensure upload directory exists */
export function ensureUploadDirExists() {
    if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
    }
}

/** Utility: Save uploaded file and return logo path + mimetype */
export async function handleFileUpload(file, id) {
    ensureUploadDirExists();
    const fileExt = path.extname(file.originalname);
    const uniqueName = `${id}${fileExt}`;
    const newFilePath = path.join(uploadPath, uniqueName);

    fs.copyFileSync(file.path, newFilePath);
    fs.unlinkSync(file.path); // delete temp file

    return {
        logoPath: `/appimages/${uniqueName}`,
        mimetype: file.mimetype,
    };
}

/** Utility: Delete existing logo file */
export function deleteOldLogo(filePath) {
    if (!filePath) return;
    const fullPath = path.join(uploadPath, path.basename(filePath));
    if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
    }
}
