import express from 'express';
const shiftRoute = express.Router();
import { validateAuthToken } from '../middlewares/validateAuthToken.js';
shiftRoute.use(validateAuthToken); // Apply token forwarding middleware to all routes

import ShiftController from '../controllers/shiftController.js';
import { createShiftSchema, updateShiftSchema, idParamSchema, shiftQuerySchema } from '../models/shiftModel.js';
import { validateRequest } from '../validations/validate.js';
shiftRoute.route('/list').get(ShiftController.getAllShift);
shiftRoute.post('/create', validateRequest({ bodySchema: createShiftSchema }), ShiftController.createShift);
shiftRoute.put('/update/:id', validateRequest({ bodySchema: updateShiftSchema, paramsSchema: idParamSchema, querySchema: shiftQuerySchema }), ShiftController.updateShift);
shiftRoute.delete('/delete/:id', validateRequest({ paramsSchema: idParamSchema }), ShiftController.deleteShift);

export default shiftRoute;
