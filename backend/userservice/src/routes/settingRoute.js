import express from 'express';
const settingRoute = express.Router();
import upload from '../middlewares/uploadLogo.js';
import { validateAuthToken } from '../middlewares/validateAuthToken.js';
settingRoute.use(validateAuthToken); // Apply token forwarding middleware to all routes


import settingController from '../controllers/settingController.js';
import { createSettingSchema, updateSettingSchema, idParamSchema, settingQuerySchema } from '../models/settingModel.js';
import { validateRequest } from '../validations/validate.js';
settingRoute.route('/list').get(settingController.getAllSetting);
settingRoute.post('/create', upload.single('logo'), validateRequest({ bodySchema: createSettingSchema }), settingController.createSetting);
settingRoute.put('/update/:id', upload.single('logo'), validateRequest({ bodySchema: updateSettingSchema, paramsSchema: idParamSchema, querySchema: settingQuerySchema }), settingController.updateSetting);

export default settingRoute;
