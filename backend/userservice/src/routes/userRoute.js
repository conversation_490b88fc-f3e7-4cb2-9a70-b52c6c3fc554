import express from 'express';
const userRoute = express.Router();

import { validateAuthToken } from '../middlewares/validateAuthToken.js';
userRoute.use(validateAuthToken); // Apply token validation to all user routes

import userController from '../controllers/userController.js';
import {
    createUserSchema,
    updateUserSchema,
    idParamSchema,
    userQuerySchema,
    changePasswordSchema
} from '../models/userModel.js';

import { validateRequest } from '../validations/validate.js';

// @route GET /user/list?role=&department=&search=&page=&limit=
userRoute.get(
    '/list',
    validateRequest({ querySchema: userQuerySchema }),
    userController.getAllUsers
);

// @route POST /user/create
userRoute.post(
    '/create',
    validateRequest({ bodySchema: createUserSchema }),
    userController.createUser
);

// @route PUT /user/update
userRoute.put(
    '/update',
    validateRequest({
        paramsSchema: idParamSchema,
        bodySchema: updateUserSchema
    }),
    userController.updateUser
);

// @route DELETE /user/delete/:id
userRoute.delete(
    '/delete/:id',
    validateRequest({ paramsSchema: idParamSchema }),
    userController.deleteUser
);

// @route GET /user/profile
userRoute.get('/profile', userController.getProfile);

// @route POST /user/change-password
userRoute.post('/change-password', validateRequest({ bodySchema: changePasswordSchema }), userController.changePassword);

export default userRoute;
