import express from 'express';
const departmentRoute = express.Router();
import upload from '../middlewares/uploadLogo.js';
import { validateAuthToken } from '../middlewares/validateAuthToken.js';
departmentRoute.use(validateAuthToken); // Apply token forwarding middleware to all routes

import departmentController from '../controllers/departmentController.js';
import { createDepartmentSchema, updateDepartmentSchema, idParamSchema, departmentQuerySchema } from '../models/departmentModel.js';
import { validateRequest } from '../validations/validate.js';

departmentRoute.route('/list').get(departmentController.getAllDepartment);
departmentRoute.post('/create', upload.single('logo'), validateRequest({ bodySchema: createDepartmentSchema }), departmentController.createDepartment);
departmentRoute.put('/update/:id', upload.single('logo'), validateRequest({ bodySchema: updateDepartmentSchema, paramsSchema: idParamSchema, querySchema: departmentQuerySchema }), departmentController.updateDepartment);
departmentRoute.delete('/delete/:id', validateRequest({ paramsSchema: idParamSchema }), departmentController.deleteDepartment);

export default departmentRoute;
