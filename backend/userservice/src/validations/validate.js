export const validateRequest = ({ bodySchema, paramsSchema, querySchema, fileRequired = false }) => {
  return (req, res, next) => {
    const errors = [];

    // Validate body
    if (bodySchema) {
      const { error, value } = bodySchema.validate(req.body, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(d => `Body: ${d.message}`));
      } else {
        req.body = value;
      }
    }

    // Validate params
    if (paramsSchema) {
      const { error, value } = paramsSchema.validate(req.params, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(d => `Params: ${d.message}`));
      } else {
        req.params = value;
      }
    }

    // Validate query
    if (querySchema) {
      const { error, value } = querySchema.validate(req.query, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(d => `Query: ${d.message}`));
      } else {
        req.query = value;
      }
    }

    if (fileRequired && !req.file) {
      errors.push('Body: Logo file is required');
    }

    if (errors.length > 0) {
      return res.status(400).json({
        status: false,
        data: null,
        error_code: 'VALIDATION_ERROR',
        errors,
        message: 'Validation failed',
      });
    }

    next();
  };
};
