import Joi from 'joi';

export const createDepartmentSchema = Joi.object({
    department_name: Joi.string().trim().min(2).required().messages({
        'string.empty': 'Department name is required',
        'string.min': 'Department name must be at least 2 characters',
        'any.required': 'Department name is required',
    }),
    logo: Joi.any().optional(),
    department_color: Joi.string().trim().allow('').optional()
});

export const idParamSchema = Joi.object({
    id: Joi.string().guid({ version: 'uuidv4' }).required().messages({
        'string.guid': 'ID must be a valid UUID',
        'any.required': 'ID is required',
    }),
});

export const updateDepartmentSchema = Joi.object({
    department_name: Joi.string().trim().min(2).required().messages({
        'string.min': 'Department name must be at least 2 characters',
        'any.required': 'Department name is required',
    })
}).unknown(true);

export const departmentQuerySchema = Joi.object({
    active: Joi.boolean().optional(),
});


