import Joi from 'joi';
import UserRoles, { AppConstants } from '../constants/userRoles.js';

// Create User
export const createUserSchema = Joi.object({
    username: Joi.string().alphanum().min(3).required().messages({
        'string.base': 'Username must be a string',
        'string.alphanum': 'Username must only contain letters and numbers',
        'string.min': 'Username must be at least 3 characters long',
        'any.required': 'Username is required',
    }),
    password: Joi.string().min(8).required().messages({
        'string.min': 'Password must be at least 8 characters',
        'any.required': 'Password is required',
    }),
    firstname: Joi.string().trim().optional(),
    lastname: Joi.string().trim().optional(),
    email: Joi.string().email().required().messages({
        'string.email': 'Email must be a valid email',
        'any.required': 'Email is required',
    }),
    phone: Joi.string().trim().optional(),
    role: Joi.string().valid(...Object.values(UserRoles)).required().messages({
        'any.only': 'Invalid role value',
        'any.required': 'Role is required',
    }),
    departments: Joi.array().items(Joi.string().guid({ version: 'uuidv4' })).optional(),
    shifts: Joi.array().items(Joi.string().guid({ version: 'uuidv4' })).optional(),
    theme: Joi.string().valid('light', 'dark').optional(),
    color_scheme: Joi.string().optional()
});

// Update User
export const updateUserSchema = Joi.object({
    firstname: Joi.string().trim().optional(),
    lastname: Joi.string().trim().optional(),
    phone: Joi.string().trim().optional(),
    departments: Joi.array().items(Joi.string().guid({ version: 'uuidv4' })).optional(),
    shifts: Joi.array().items(Joi.string().guid({ version: 'uuidv4' })).optional(),
    role: Joi.string().valid(...Object.values(UserRoles)).optional(),
    email: Joi.string().email().optional(),
    username: Joi.string().alphanum().min(3).optional().messages({
        'string.base': 'Username must be a string',
        'string.alphanum': 'Username must only contain letters and numbers',
        'string.min': 'Username must be at least 3 characters long',
        'any.required': 'Username is required',
    }),  
});

// User ID (param validation)
export const idParamSchema = Joi.object({
    id: Joi.string().guid({ version: 'uuidv4' }).optional().messages({
        'string.guid': 'ID must be a valid UUID',
        'any.required': 'ID is required',
    }),
});

// Optional query params for list filtering
export const userQuerySchema = Joi.object({
    role: Joi.string().valid(...Object.values(UserRoles)).optional(),
    department: Joi.string().optional(),
    search: Joi.string().optional(),
    page: Joi.number().min(1).optional(),
    limit: Joi.number().min(1).optional(),
    sortBy: Joi.string().valid('username', 'email', 'firstname', 'lastname', 'role').optional(),
    sortDirection: Joi.string().valid('asc', 'desc').optional()
});

// Change Password
export const changePasswordSchema = Joi.object({
    id: Joi.string().guid({ version: 'uuidv4' }).optional().messages({
        'string.guid': 'Invalid user ID format'
    }),
    username: Joi.string().required().messages({
        'string.empty': 'Username is required'
    }),
    oldPassword: Joi.string().required().messages({
        'string.empty': 'Old password is required'
    }),
    newPassword: Joi.string()
        .pattern(new RegExp(AppConstants.PASSWORD_POLICY_REGEX))
        .required()
        .messages({
            'string.pattern.base': 'Password must be 8-16 chars, include upper, lower, number, and special char'
        })
});