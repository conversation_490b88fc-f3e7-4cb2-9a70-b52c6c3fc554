import Joi from 'joi';

export const createSettingSchema = Joi.object({
  company_name: Joi.string().trim().allow('').optional(),
  theme: Joi.string().trim().allow('').optional(),
  logo: Joi.any().optional()
});

export const idParamSchema = Joi.object({
  id: Joi.string().guid({ version: 'uuidv4' }).required().messages({
    'string.guid': 'ID must be a valid UUID',
    'any.required': 'ID is required',
  }),
});

export const updateSettingSchema = Joi.object({
  company_name: Joi.string().trim().allow('').optional(),
  theme: Joi.string().trim().allow('').optional(),
  logo: Joi.any().optional()
}).unknown(true);

export const settingQuerySchema = Joi.object({
  active: Joi.boolean().optional(),
});
