import Joi from 'joi';

// Define reusable time string schema
const timeString = Joi.string().trim().required().messages({
  'string.empty': 'Time is required',
  'any.required': 'Time is required',
});

// Define the break time object schema
const breakTimeSchema = Joi.array().items(
  Joi.object({
    start_time: timeString.label('Break Start Time'),
    end_time: timeString.label('Break End Time'),
  })
).messages({
  'array.base': 'Break time must be an array of objects',
});

// Define UUID schema for departments
const departmentUUIDs = Joi.array().items(
  Joi.string().uuid().messages({
    'string.guid': 'Each department must be a valid UUID',
  })
).messages({
  'array.base': 'Departments must be an array of UUIDs',
});

export const createShiftSchema = Joi.object({
  shift_name: Joi.string().trim().min(2).required().messages({
    'string.empty': 'Shift name is required',
    'string.min': 'Shift name must be at least 2 characters',
    'any.required': 'Shift name is required',
  }),
  time_format: Joi.string().trim().required().messages({
    'string.empty': 'Time format is required',
    'any.required': 'Time format is required',
  }),
  start_time: Joi.string().trim().required().messages({
    'string.empty': 'Start Time is required',
    'any.required': 'Start Time is required',
  }),
  end_time: Joi.string().trim().required().messages({
    'string.empty': 'End Time is required',
    'any.required': 'End Time is required',
  }),
  start_time: timeString.label('start_time'),
  end_time: timeString.label('end_time'),
  break_time: breakTimeSchema,
  departments: departmentUUIDs,
});

export const idParamSchema = Joi.object({
  id: Joi.string().guid({ version: 'uuidv4' }).required().messages({
    'string.guid': 'ID must be a valid UUID',
    'any.required': 'ID is required',
  }),
});

export const updateShiftSchema = Joi.object({
  shift_name: Joi.string().trim().min(2).required().messages({
    'string.empty': 'Shift name is required',
    'string.min': 'Shift name must be at least 2 characters',
    'any.required': 'Shift name is required',
  }),
  time_format: Joi.string().trim().required().messages({
    'string.empty': 'Time format is required',
    'any.required': 'Time format is required',
  }),
  start_time: Joi.string().trim().required().messages({
    'string.empty': 'Start Time is required',
    'any.required': 'Start Time is required',
  }),
  end_time: Joi.string().trim().required().messages({
    'string.empty': 'End Time is required',
    'any.required': 'End Time is required',
  }),
  start_time: timeString.label('start_time'),
  end_time: timeString.label('end_time'),
  break_time: breakTimeSchema,
  departments: departmentUUIDs,
});

export const shiftQuerySchema = Joi.object({
  active: Joi.boolean().optional(),
});


