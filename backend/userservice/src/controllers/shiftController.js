import responseFormatter from '../middlewares/responseFormator.js';
import pool from '../config/db.js';
import queries from '../utils/queries/shiftQueries.js';
import StatusCodes from '../config/statusCodes.js';
import UserRoles from '../constants/userRoles.js';
const { getListQueryForAdmin, getListQueryForSuperAdmin, createQuery, updateQuery, deleteQuery, userCheckQuery, shiftExistsQuery, shiftTimeOverlapCreateQuery, shiftTimeOverlapUpdateQuery } = queries;
const formatter = new responseFormatter();

//@desc get all shift
//@route GET /api/v1/shift/list
//@access private
const getAllShift = async (req, res) => {
    const loginRole = req.user.role?.toLowerCase();

    if (loginRole !== UserRoles.SUPERADMIN && loginRole !== UserRoles.ADMIN) {
        return formatter.error('Unauthorized access', null, res, StatusCodes.UNAUTHORIZED);
    }

    try {
        let query = getListQueryForSuperAdmin;
        let values = [];

        if (loginRole === UserRoles.ADMIN) {
            // For admin, filter shifts by the user's ID
            query = getListQueryForAdmin;
            values = [req.user.id];
        }

        const { rows } = await pool.query(query, values);
        return formatter.success(rows, null, res);
    } catch (error) {
        console.error('Failed to fetch shifts:', error);
        return formatter.error('Failed to fetch shifts', error, res);
    }
};


//@desc create shift
//@route POST /api/v1/shift/create
//@access private
const createShift = async (req, res) => {
    const loginRole = req.user.role.toLowerCase();
    if (loginRole !== UserRoles.SUPERADMIN) {
        return formatter.error('Unauthorized access', null, res, StatusCodes.UNAUTHORIZED);
    }
    const { shift_name, time_format, start_time, end_time, break_time, departments } = req.body;
    try {
        // Atleast one department needed
        if (!departments || departments.length === 0) {
            return formatter.error('Select at least one department.', null, res, StatusCodes.BAD_REQUEST);
        }
        // Check if shift_name already exists
        const existing = await pool.query(shiftExistsQuery, [shift_name]);
        if (existing.rows.length > 0) {
            return formatter.error('Shift name already exists.', null, res, StatusCodes.CONFLICT);
        }
        // Check for overlapping shift times
        const overlap = await pool.query(shiftTimeOverlapCreateQuery, [start_time, end_time]);
        if (overlap.rows.length > 0) {
            return formatter.error('Shift time overlaps with an existing shift.', null, res, StatusCodes.CONFLICT);
        }
        const values = [shift_name, time_format, start_time, end_time, JSON.stringify(break_time), departments || null];
        const { rows } = await pool.query(createQuery, values);
        formatter.success(rows[0], null, res, 'Shift created successfully.');
    } catch (error) {
        console.error('Error creating shift:', error);
        formatter.error('Failed to create shift', error, res);
    }
};

//@desc update shift
//@route PUT /api/v1/shift/update/:id
//@access private
const updateShift = async (req, res) => {
    const loginRole = req.user.role.toLowerCase();
    if (loginRole !== UserRoles.SUPERADMIN) {
        return formatter.error('Unauthorized access', null, res, StatusCodes.UNAUTHORIZED);
    }
    const { id } = req.params;
    const { shift_name, time_format, start_time, end_time, break_time, departments } = req.body;
    try {
        // Atleast one department needed
        if (!departments || departments.length === 0) {
            return formatter.error('Select at least one department.', null, res, StatusCodes.BAD_REQUEST);
        }

        // Check for overlapping shift times
        const overlap = await pool.query(shiftTimeOverlapUpdateQuery, [id, start_time, end_time]);
        if (overlap.rows.length > 0) {
            return formatter.error('Shift time overlaps with an existing shift.', null, res, StatusCodes.CONFLICT);
        }
        const values = [shift_name, time_format, start_time, end_time, JSON.stringify(break_time), departments, id || null];
        const { rows } = await pool.query(updateQuery, values);
        if (rows.length === 0) {
            return formatter.error('shift not found', null, res, StatusCodes.NOT_FOUND);
        }

        formatter.success(rows[0], null, res, 'Shift updated successfully.');
    } catch (error) {
        console.error('Error updating shift:', error);
        formatter.error('Failed to update shift', error, res);
    }
};

//@desc delete shift
//@route DELETE /api/v1/shift/delete/:id
//@access private
const deleteShift = async (req, res) => {
    const loginRole = req.user.role.toLowerCase();
    if (loginRole !== UserRoles.SUPERADMIN) {
        return formatter.error('Unauthorized access', null, res, StatusCodes.UNAUTHORIZED);
    }
    const { id } = req.params;
    try {
        // Check if any users are linked to the department
        const { rowCount: userCount } = await pool.query(userCheckQuery, [id]);
        if (userCount > 0) {
            return formatter.error('Cannot delete shift. It is assigned to one or more users.', null, res, StatusCodes.BAD_REQUEST);
        }
        const { rows } = await pool.query(deleteQuery, [id]);
        if (rows.length === 0) {
            return formatter.error('shift not found', null, res, StatusCodes.NOT_FOUND);
        }
        formatter.success(rows[0], null, res, 'Shift deleted successfully.');
    } catch (error) {
        console.error('Error deleting shift:', error);
        formatter.error('Failed to delete shift', error, res);
    }
};

export default { getAllShift, createShift, updateShift, deleteShift }
