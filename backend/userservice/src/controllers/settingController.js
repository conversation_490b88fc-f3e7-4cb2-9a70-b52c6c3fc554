import responseFormatter from '../middlewares/responseFormator.js';
import pool from '../config/db.js';
import queries from '../utils/queries/settingQueries.js';
import StatusCodes from '../config/statusCodes.js';
import { handleFileUpload, deleteOldLogo } from '../utils/common/commonCode.js';
import { v4 as uuidv4 } from 'uuid';

const { getListQuery, createQuery, updateQuery, getSettingById } = queries;
const formatter = new responseFormatter();

//@desc Get all settings
//@route GET /api/v1/setting/list
//@access Private
const getAllSetting = async (req, res) => {
    try {
        const { rows } = await pool.query(getListQuery);
        // Add logo URL if needed in future
        formatter.success(rows, null, res, 'Settings fetched successfully.');
    } catch (error) {
        console.error('Error fetching settings:', error);
        formatter.error('Failed to fetch settings', error, res);
    }
};

//@desc Create setting
//@route POST /api/v1/setting/create
//@access Private
const createSetting = async (req, res) => {
    const file = req.file;
    const id = uuidv4();

    try {
        let logoPath = null;
        let mimetype = null;

        if (file) {
            ({ logoPath, mimetype } = await handleFileUpload(file, id));
        }

        const company_name = req.body.company_name?.trim() || null;
        const theme = req.body.theme?.trim() || null;

        const values = [id, company_name, theme, logoPath, mimetype];
        const { rows } = await pool.query(createQuery, values);
        formatter.success(rows[0], null, res, 'Setting inserted successfully.');
    } catch (error) {
        console.error('Error inserting setting:', error);
        formatter.error('Failed to insert setting', error, res);
    }
};

//@desc Update setting
//@route PUT /api/v1/setting/:id
//@access Private
const updateSetting = async (req, res) => {
    const { id } = req.params;
    if (!id) return formatter.error('Missing setting ID.', null, res, StatusCodes.BAD_REQUEST);

    const file = req.file;
    const company_name = req.body.company_name?.trim() ?? null;
    const theme = req.body.theme?.trim() ?? null;

    try {
        const { rows: existingRows } = await pool.query(getSettingById, [id]);
        if (existingRows.length === 0) {
            return formatter.notFound(res, 'Setting not found');
        }

        const existing = existingRows[0];
        let logoPath = existing.logo_path;
        let mimetype = existing.mimetype;
        if (file) {
            deleteOldLogo(existing.logo_path);
            ({ logoPath, mimetype } = await handleFileUpload(file, id));
        }

        const values = [
            company_name ?? existing.company_name,
            theme ?? existing.theme,
            logoPath,
            mimetype,
            id
        ];

        const { rows } = await pool.query(updateQuery, values);
        formatter.success(rows[0], null, res, 'Setting updated successfully.');
    } catch (error) {
        console.error('Error updating setting:', error);
        formatter.error('Failed to update setting', error, res);
    }
};

export default {
    getAllSetting,
    createSetting,
    updateSetting
};
