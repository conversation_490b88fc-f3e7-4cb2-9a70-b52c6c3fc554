import responseFormatter from '../middlewares/responseFormator.js';
import pool from '../config/db.js';
import queries from '../utils/queries/departmentQueries.js';
import { handleFileUpload, deleteOldLogo } from '../utils/common/commonCode.js';
import { v4 as uuidv4 } from 'uuid';
import StatusCodes from '../config/statusCodes.js';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat.js';
import UserRoles from '../constants/userRoles.js';
dayjs.extend(customParseFormat);

const { getListQuery, createQuery, updateQuery, deleteQuery, getDepartmentByIdQuery, getDepartmentByAdminIdQuery,
    getAdminListQuery, getLoginUsersDetails, getShiftListQuery, getUserCountQuery } = queries;
const formatter = new responseFormatter();

//@desc get all departments
//@route GET /api/v1/department/list
//@access private
const getAllDepartment = async (req, res) => {
    try {
        const loginRole = req.user.role;
        const loginUserId = req.user.id;

        let departments = [];
        let users = [];

        // Fetch users based on role
        if (loginRole === UserRoles.SUPERADMIN) {
            const { rows } = await pool.query(getAdminListQuery);
            users = rows;
            const { rows: allDepartments } = await pool.query(getListQuery);
            departments = allDepartments;
        } else {
            const { rows } = await pool.query(getLoginUsersDetails, [loginUserId]);
            users = rows;
            const { rows: adminDepartments } = await pool.query(getDepartmentByAdminIdQuery, [users[0].departments]);
            departments = adminDepartments;
        }

        // Fetch all shifts
        const { rows: shifts } = await pool.query(getShiftListQuery);
        const now = dayjs();

        // Build department-wise response
        const response = await Promise.all(departments.map(async (dept) => {
            const deptId = dept.id;
            const deptName = dept.department_name;

            const deptShifts = shifts.filter(shift => shift.departments?.includes(deptId));

            const activeShift = deptShifts.find(shift => {
                const start = dayjs(shift.start_time, ['hh:mm A']);
                const end = dayjs(shift.end_time, ['hh:mm A']);

                return end.isAfter(start)
                    ? now.isAfter(start) && now.isBefore(end)
                    : now.isAfter(start) || now.isBefore(end);
            });

            let managerName = null;
            let shiftName = activeShift?.shift_name ? activeShift.shift_name : null
            let usersInShift = 0;

            if (activeShift && users.length) {
                const manager = users.find(user =>
                    user.departments.includes(deptId) &&
                    user.shifts.includes(activeShift.id)
                );

                if (manager) {
                    managerName = `${manager.firstname} ${manager.lastname}`;
                }

                try {
                    const { rows: countRows } = await pool.query(getUserCountQuery, [activeShift.id, deptId]);
                    if (countRows.length) {
                        usersInShift = parseInt(countRows[0].count);
                    }
                } catch (error) {
                    console.error('Error fetching user count:', error);
                    usersInShift = 0; // Fallback to 0 if error occurs
                }

            }
            // TODO: reciever, transmitter, alert, offline counts are hardcoded for now
            // These should be replaced with actual logic to fetch counts
            // For example, you can query the database to get these counts based on the department and shift
            // For now, we will return hardcoded values
            return {
                id: deptId,
                name: deptName,
                shift: shiftName,
                managerName,
                logo_path: dept.logo_path,
                mimetype: dept.mimetype,
                department_color: dept.department_color,
                count: {
                    reciever: 4,
                    transmitter: 22,
                    users: usersInShift,
                    alert: 7,
                    offline: 2,
                },
            };
        }));

        return formatter.success(response, null, res);

    } catch (error) {
        console.error('Error in getAllDepartment:', error);
        return formatter.error('Failed to fetch Departments', error, res);
    }
};

//@desc Create department
//@route POST /api/v1/department/create
//@access private
const createDepartment = async (req, res) => {
    const { department_name, department_color } = req.body;
    const file = req.file;
    const id = uuidv4();
    const loginRole = req.user.role;
    if (loginRole !== UserRoles.SUPERADMIN) {
        return formatter.error('Only superadmin can create departments.', null, res, StatusCodes.FORBIDDEN);
    }
    try {
        //Check if department_name already exists
        const existing = await pool.query(queries.departmentExistsQuery, [department_name]);

        if (existing.rows.length > 0) {
            return formatter.error('Department name already exists.', null, res, StatusCodes.CONFLICT);
        }

        let logoPath = null;
        let mimetype = null;
        if (file) {
            ({ logoPath, mimetype } = await handleFileUpload(file, id));
        }
        const values = [id, department_name, logoPath, mimetype, department_color];
        const { rows } = await pool.query(createQuery, values);
        return formatter.success(rows[0], null, res, 'Department created successfully.');
    } catch (error) {
        console.error('Error creating department:', error);
        return formatter.error('Failed to create department', error, res);
    }
};

//@desc update department
//@route PUT /api/v1/department/update/:id
//@access private
const updateDepartment = async (req, res) => {
    const { department_name } = req.body;
    const { id } = req.params;
    const file = req.file;
    const loginRole = req.user.role;
    if (loginRole == UserRoles.SUPERADMIN || loginRole == UserRoles.ADMIN) {
        try {
            // Fetch existing department
            const { rows: existingRows } = await pool.query(getDepartmentByIdQuery, [id]);
            if (existingRows.length === 0) {
                return formatter.error('Department not found', null, res, StatusCodes.NOT_FOUND);
            }

            const existing = existingRows[0];
            let logoPath = existing.logo_path;
            let mimetype = existing.mimetype;

            // Handle new logo upload
            if (file) {
                deleteOldLogo(existing.logo_path);
                ({ logoPath, mimetype } = await handleFileUpload(file, id));
            } else {
                // deleteOldLogo(existing.logo_path);
                logoPath = existing.logo_path ? existing.logo_path : null;
                mimetype = existing.mimetype ? existing.mimetype : null;
            }

            const values = [
                department_name ?? existing.department_name,
                logoPath,
                mimetype,
                id
            ];

            const { rows } = await pool.query(updateQuery, values);
            formatter.success(rows[0], null, res, 'Department updated successfully.');
        } catch (error) {
            console.error('Error updating department:', error);
            formatter.error('Failed to update department', error, res);
        }
    } else {
        return formatter.error('Only superadmin can update departments.', null, res, StatusCodes.FORBIDDEN);

    }
};

//@desc delete department
//@route DELETE /api/v1/department/delete/:id
//@access private
const deleteDepartment = async (req, res) => {
    const { id } = req.params;
    const loginRole = req.user.role;
    if (loginRole !== UserRoles.SUPERADMIN) {
        return formatter.error('Only superadmin can delete departments.', null, res, StatusCodes.FORBIDDEN);
    }
    try {
        // Check if any users are linked to the department
        const { rowCount: userCount } = await pool.query(queries.userCheckQuery, [id]);
        if (userCount > 0) {
            return formatter.error('Cannot delete department. It is assigned to one or more users.', null, res, StatusCodes.BAD_REQUEST);
        }

        // Check if department exists
        const { rows: existingRows } = await pool.query(getDepartmentByIdQuery, [id]);
        if (existingRows.length === 0) {
            return formatter.error('Department not found', null, res, StatusCodes.NOT_FOUND);
        }

        const existing = existingRows[0];

        // Delete logo file if exists
        if (existing.logo_path) {
            deleteOldLogo(existing.logo_path);
        }

        // Delete from DB
        const { rows } = await pool.query(deleteQuery, [id]);
        formatter.success(rows[0], null, res, 'Department deleted successfully.');
    } catch (error) {
        console.error('Error deleting department:', error);
        formatter.error('Failed to delete department', error, res);
    }

};

export default { getAllDepartment, createDepartment, updateDepartment, deleteDepartment }
