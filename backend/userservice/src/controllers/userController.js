import responseFormatter from '../middlewares/responseFormator.js';
import pool from '../config/db.js';
import userQueries from '../utils/queries/userQueries.js';
import departmentQueries from '../utils/queries/departmentQueries.js';
import shiftQueries from '../utils/queries/shiftQueries.js';
import bcrypt from 'bcrypt';
import UserRoles from '../constants/userRoles.js';
import StatusCodes from '../config/statusCodes.js';
import { blockUserTokens } from '../middlewares/blockUserTokens.js';

const {
    getFilteredListQuery,
    getFilteredUsersCountQuery,
    getProfileQuery,
    createQuery,
    deleteQuery,
    getUserByIdAndUsernameQuery,
    updatePasswordQuery
} = userQueries;

const formatter = new responseFormatter();

// @desc    Get all users (with optional filters)
// @route   GET /user/list
// @access  private
const getAllUsers = async (req, res) => {
    try {
        const { search = null, page = 1, limit = 10, sortBy = 'created_at', sortDirection = 'desc' } = req.query;
        let department = null;
        const offset = (page - 1) * limit;
        const userRole = req.user.role ? req.user.role.toLowerCase() : null;
        let queryRole = userRole;
        if (queryRole && !Object.values(UserRoles).includes(queryRole)) {
            return formatter.error('Invalid role value', null, res, StatusCodes.BAD_REQUEST);
        } else {
            switch (queryRole) {
                case UserRoles.SUPERADMIN:
                    queryRole = [UserRoles.ADMIN];
                    break;
                case UserRoles.ADMIN:
                    queryRole = [UserRoles.USERROLE1, UserRoles.USERROLE2];
                    // Get admin's departments from DB, use as filter
                    const adminDepartmentsRes = await pool.query(userQueries.getDepartmentsByUserIdQuery, [req.user.id]);
                    if (!adminDepartmentsRes.rows.length) {
                        return formatter.error('Admin user not found', null, res, StatusCodes.NOT_FOUND);
                    }
                    const adminDepartments = adminDepartmentsRes.rows[0].departments || [];
                    if (!adminDepartments.length) {
                        return formatter.error('No departments assigned to admin', null, res, StatusCodes.FORBIDDEN);
                    }
                    department = adminDepartments;
                    break;
                default:
                    queryRole = [queryRole];
                    break;
            }
        }

        // Validate sortBy and sortDirection
        const allowedSortBy = ['username', 'email', 'firstname', 'lastname', 'role', 'department', 'created_at'];
        const allowedSortDirection = ['asc', 'desc'];
        // Default to created_at if sortBy is not present or invalid
        const sortColumn = allowedSortBy.includes(sortBy) ? sortBy : 'created_at';
        const sortOrder = allowedSortDirection.includes(sortDirection.toLowerCase()) ? sortDirection.toLowerCase() : 'desc';

        // Add department id as a filter in getFilteredListQuery
        // If department is an array (admin), pass as array; else pass as single value
        const values = [
            queryRole || null,
            department || null,
            search || null,
            parseInt(limit, 10),
            parseInt(offset, 10)
        ];
        const countValues = values.slice(0, 3); // only first 3 values (without limit & offset)

        // Inject ORDER BY clause into query string
        let filteredListQuery = getFilteredListQuery.replace('/*ORDER_BY*/', `ORDER BY ${sortColumn} ${sortOrder}`);

        const [dataResult, countResult] = await Promise.all([
            pool.query(filteredListQuery, values),
            pool.query(getFilteredUsersCountQuery, countValues)
        ]);

        const total = parseInt(countResult.rows[0].total, 10);
        // Map 'admin' role to 'manager' in response
        const mappedUsers = dataResult.rows.map(user => ({
            ...user,
            role: user.role === 'admin' ? 'manager' : user.role
        }));
        formatter.success(
            {
                users: mappedUsers,
                total,
                page: parseInt(page),
                limit: parseInt(limit)
            },
            null,
            res
        );
    } catch (error) {
        formatter.error('Failed to fetch users', error, res);
    }
};

// @desc    Create new user
// @route   POST /user/create
// @access  private
const createUser = async (req, res) => {
    try {
        const {
            username,
            password,
            firstname,
            lastname,
            email,
            phone,
            role,
            departments = [],
            shifts = [],
            theme = 'light',
            color_scheme = '#4097db'
        } = req.body;

        // Check for existing username or email
        const checkQuery = `SELECT id, username, email FROM users WHERE (username = $1 OR email = $2) AND is_deleted = FALSE LIMIT 1;`;
        const checkResult = await pool.query(checkQuery, [username, email]);
        if (checkResult.rows.length > 0) {
            const existing = checkResult.rows[0];
            if (existing.username === username && existing.email === email) {
                return formatter.error('Both username and email already exist', null, res, StatusCodes.CONFLICT);
            } else if (existing.username === username) {
                return formatter.error('Username already exists', null, res, StatusCodes.CONFLICT);
            } else {
                return formatter.error('Email already exists', null, res, StatusCodes.CONFLICT);
            }
        }

        // generate a hashed password
        const hashedPassword = await bcrypt.hash(password, 10);
        const values = [
            username, hashedPassword, firstname, lastname, email, phone, role, departments, shifts, theme, color_scheme
        ];

        const { rows } = await pool.query(createQuery, values);
        formatter.success(rows[0], null, res, 'User created successfully');
    } catch (error) {
        // Handle unique constraint violation gracefully
        if (error.code === '23505') {
            // Try to parse which field caused the violation
            if (error.detail && error.detail.includes('username')) {
                return formatter.error('Username already exists', null, res, StatusCodes.CONFLICT);
            } else if (error.detail && error.detail.includes('email')) {
                return formatter.error('Email already exists', null, res, StatusCodes.CONFLICT);
            } else {
                return formatter.error('Username or email already exists', null, res, StatusCodes.CONFLICT);
            }
        }
        formatter.error('Failed to create user', error, res);
    }
};

// @desc    Update existing user
// @route   PUT /user/update/:id
// @access  private
const updateUser = async (req, res) => {
    try {
        // Accept id from either params or query
        const paramId = req.params.id || req.query.id;
        const allowedFields = ['firstname', 'lastname', 'phone', 'email', 'username', 'departments', 'shifts'];
        const updateFields = Object.keys(req.body).filter(key => allowedFields.includes(key));
        if (updateFields.length === 0) {
            return formatter.error('No valid fields to update', null, res, StatusCodes.BAD_REQUEST);
        }

        // Role-based validation
        const currentUserId = req.user.id;
        const currentUserRole = req.user.role ? req.user.role.toLowerCase() : null;
        let targetUserId = paramId;
        // If user is editing their own details, use req.user.id
        if (!paramId) {
            targetUserId = currentUserId;
        } else {
            // Only allow superadmin to edit admin, and admin to edit userrole1/2
            // Fetch target user's role
            const targetUserRes = await pool.query(getProfileQuery, [paramId]);
            if (!targetUserRes.rows.length) {
                return formatter.error('Target user not found', null, res, StatusCodes.NOT_FOUND);
            }
            const targetUserRole = targetUserRes.rows[0].role ? targetUserRes.rows[0].role.toLowerCase() : null;

            if (
                (currentUserRole === UserRoles.SUPERADMIN && targetUserRole === UserRoles.ADMIN) ||
                (currentUserRole === UserRoles.ADMIN && (targetUserRole === UserRoles.USERROLE1 || targetUserRole === UserRoles.USERROLE2))
            ) {
                // Allowed
            } else {
                return formatter.error('Permission denied', null, res, StatusCodes.FORBIDDEN);
            }
        }

        // Build dynamic update query
        const setClause = updateFields.map((field, idx) => `${field} = $${idx + 1}`).join(', ');
        const values = updateFields.map(field => req.body[field]);
        values.push(targetUserId); // last value is user id

        const dynamicUpdateQuery = `UPDATE users SET ${setClause} WHERE id = $${values.length} RETURNING *`;
        const { rows } = await pool.query(dynamicUpdateQuery, values);
        if (rows.length === 0) return formatter.error('User not found', null, res, StatusCodes.NOT_FOUND);

        formatter.success(rows[0], null, res, 'User updated successfully');
    } catch (error) {
        // Handle unique constraint violation gracefully
        if (error.code === '23505') {
            if (error.detail && error.detail.includes('username')) {
                return formatter.error('Username already exists', null, res, StatusCodes.CONFLICT);
            } else if (error.detail && error.detail.includes('email')) {
                return formatter.error('Email already exists', null, res, StatusCodes.CONFLICT);
            } else {
                return formatter.error('Username or email already exists', null, res, StatusCodes.CONFLICT);
            }
        }
        formatter.error('Failed to update user', error, res);
    }
};

// @desc    Delete user (soft delete)
// @route   DELETE /user/delete/:id
// @access  private
const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        // Block all tokens for this user by userId only
        await blockUserTokens(id);
        const { rows } = await pool.query(deleteQuery, [id]);
        if (rows.length === 0) return formatter.error('User not found', null, res, StatusCodes.NOT_FOUND);
        formatter.success([], null, res, 'User deleted successfully');
    } catch (error) {
        formatter.error('Failed to delete user', error, res);
    }
};

// @desc    Get logged-in user's profile
// @route   GET /user/profile
// @access  private
const getProfile = async (req, res) => {
    try {
        const userId = req.user.id; // Set via auth middleware
        const allUsersQuery = `SELECT id, username, firstname, lastname, email, phone, role, departments, shifts, theme, color_scheme FROM users WHERE is_deleted = FALSE`;
        // Fetch user profile
        const allUsersData = await pool.query(allUsersQuery);
        const { rows } = await pool.query(getProfileQuery, [userId]);
        if (rows.length === 0) return formatter.error('User not found', null, res, StatusCodes.NOT_FOUND);

        const user = rows[0];
        const userRole = req.user.role ? req.user.role.toLowerCase() : null;

        // Departments
        let departments = [];
        if (userRole === UserRoles.SUPERADMIN) {
            // All department names
            const deptResult = await pool.query(departmentQueries.getListQuery);
            departments = deptResult.rows.map(d => d.name || d.department_name);
        } else if (user.departments && user.departments.length > 0) {
            const deptResult = await pool.query(departmentQueries.getDepartmentByAdminIdQuery, [user.departments]);
            departments = deptResult.rows.map(d => d.name || d.department_name);
        }

        // Shifts
        let shifts = [];
        if (userRole === UserRoles.SUPERADMIN) {
            const shiftResult = await pool.query(shiftQueries.getListQueryForSuperAdmin);
            shifts = shiftResult.rows.map(s => s.name || s.shift_name);
        } else if (user.shifts && user.shifts.length > 0) {
            const shiftResult = await pool.query(shiftQueries.getListQueryForAdmin, [userId]);
            shifts = shiftResult.rows.map(s => s.name || s.shift_name);
        }


        // If role is admin, return manager in response
        let role = user.role;
        if (role && role.toLowerCase() === 'admin') {
            role = 'manager';
        }

        const profile = {
            ...user,
            role,
            departments,
            shifts
        };

        formatter.success(profile, null, res);
    } catch (error) {
        formatter.error('Failed to fetch profile', error, res);
    }
};

// @desc    Change user password
// @route   POST /user/change-password
// @access  private
const changePassword = async (req, res) => {
    const { username, oldPassword, newPassword } = req.body;
    try {
        const id = req.user.id; // Get user ID from auth middleware
        const { rows } = await pool.query(getUserByIdAndUsernameQuery, [id, username]);
        if (!rows.length) {
            return formatter.error('User not found or inactive', null, res, StatusCodes.NOT_FOUND);
        }

        const user = rows[0];
        const isMatch = await bcrypt.compare(oldPassword, user.password);
        if (!isMatch) {
            return formatter.error('Old password is incorrect', null, res, StatusCodes.BAD_REQUEST);
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        const result = await pool.query(updatePasswordQuery, [hashedPassword, id]);

        // Block the user's token after password change
        const token = req.headers['authorization']?.split(' ')[1];
        await blockUserTokens(id, token);
        formatter.success(result.rows[0], null, res, 'Password changed successfully');
    } catch (error) {
        formatter.error('Error changing password', error, res);
    }
};

export default {
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    getProfile,
    changePassword,
};