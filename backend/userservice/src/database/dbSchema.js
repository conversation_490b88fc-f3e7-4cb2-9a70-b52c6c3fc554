import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pool from '../config/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const initSqlPath = path.join(__dirname, 'init-db.sql');
const initSql = fs.readFileSync(initSqlPath, 'utf-8');

const runDbSetup = async () => {
  try {
    await pool.query(initSql);
    console.log('✅ Database initialized successfully');
    process.exit(0);
  } catch (err) {
    console.error('❌ Error running database init script:', err);
    process.exit(1);
  }
};

runDbSetup();
