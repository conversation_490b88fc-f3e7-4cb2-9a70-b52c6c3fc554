-- Enable extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT UNIQUE NOT NULL,         -- Login ID
  password TEXT NOT NULL,                -- Hashed
  firstname TEXT,
  lastname TEXT,
  email TEXT UNIQUE,               -- Ensure email is unique
  phone TEXT,
  role TEXT NOT NULL CHECK (role IN ('superadmin', 'admin', 'userrole1', 'userrole2')),
  departments UUID[] DEFAULT '{}',       -- References departments
  shifts UUID[] DEFAULT '{}',            -- References shifts
  theme TEXT DEFAULT 'light',            -- UI preference
  color_scheme TEXT DEFAULT '#4097db',
  must_reset_password BOOLEAN DEFAULT TRUE,
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
ALTER TABLE users
  ADD COLUMN IF NOT EXISTS phone TEXT,
  ADD COLUMN IF NOT EXISTS theme TEXT DEFAULT 'light',
  ADD COLUMN IF NOT EXISTS color_scheme TEXT DEFAULT '#4097db';

-- Create departments table
CREATE TABLE IF NOT EXISTS departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  department_name TEXT NOT NULL,
  logo_path TEXT,
  mimetype TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create shift table
CREATE TABLE IF NOT EXISTS shifts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shift_name TEXT NOT NULL,
  time_format TEXT,
  start_time TEXT, 
  end_time TEXT,
  break_time JSONB,        -- Array of break objects (start_time & endtime)
  departments UUID[],       -- Array of department IDs
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS settings (
    id VARCHAR(100) PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    theme VARCHAR(100),
    logo_path TEXT,
	  mimetype TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE settings
ALTER COLUMN theme TYPE TEXT;
ALTER TABLE settings ALTER COLUMN company_name DROP NOT NULL;


CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create TRIGGER for auto update updated_at field in departments table
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'set_updated_at'
    ) THEN
        CREATE TRIGGER set_updated_at
        BEFORE UPDATE ON departments
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END;
$$;


-- Create TRIGGER for auto update updated_at field in shifts table

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'set_updated_at'
    ) THEN
        CREATE TRIGGER set_updated_at
        BEFORE UPDATE ON shifts
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END;
$$;


-- Create TRIGGER for auto update updated_at field in settings table

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'set_updated_at'
        AND tgrelid = 'settings'::regclass
    ) THEN
        CREATE TRIGGER set_updated_at
        BEFORE UPDATE ON settings
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END;
$$;


-- solution to create all triggers
-- SELECT
--   'CREATE TRIGGER set_updated_at BEFORE UPDATE ON ' || table_name ||
--   ' FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();'
-- FROM information_schema.columns
-- WHERE column_name = 'updated_at'
--   AND table_schema = 'public';