import express from 'express';
import cors from 'cors'; // <--- Import CORS

import userRoute from './src/routes/userRoute.js';
import shiftRoute from './src/routes/shiftRoute.js';
import departmentRoute from './src/routes/departmentRoute.js';
import settingRoute from './src/routes/settingRoute.js';

const app = express();
const BASE_ROUT = process.env.BASE_ROUT || '/api/v1/';

app.use('/appimages', express.static('/app/images'));
app.use(cors()); // <--- Enable CORS for all routes
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Mount user management module routes
app.use(BASE_ROUT + 'user', userRoute);
app.use(BASE_ROUT + 'shift', shiftRoute);
app.use(BASE_ROUT + 'department', departmentRoute);
app.use(BASE_ROUT + 'setting', settingRoute);

app.get('/health', (_, res) =>
  res.json({ ok: true, service: 'user-service' })
);

// Optional: Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const port = process.env.PORT || 3001;

app.listen(port, () =>
  console.log(`User-service running on http://localhost:${port}`)
);
