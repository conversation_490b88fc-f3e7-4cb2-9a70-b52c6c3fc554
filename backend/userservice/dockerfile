FROM node:22-alpine

ENV TZ=Asia/Kolkata

# Install required packages
RUN apk add --no-cache tzdata postgresql-client dos2unix

WORKDIR /userservice

# Copy dependency files and install packages
COPY package.json package-lock.json ./
RUN npm ci --omit=dev

# Copy the rest of the app, including the shell script
COPY . .

# Convert line endings + make the shell script executable
RUN dos2unix wait-for-postgres.sh && chmod +x wait-for-postgres.sh

EXPOSE 3001

ENTRYPOINT ["./wait-for-postgres.sh", "timescaledb", "npm", "start"]
