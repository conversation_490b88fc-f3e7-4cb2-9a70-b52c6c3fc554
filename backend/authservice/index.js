import express from 'express';
import cors from 'cors'; // <--- Import CORS

import authRoute from './src/routes/authRoutes.js';
import initializeSuperAdmin from './src/controllers/initService.js'; // Import the initialization function

const app = express();

app.use(cors()); // <--- Enable CORS for all routes

app.use(express.urlencoded({ extended: true }));
app.use(express.json());

const BASE_ROUTE = process.env.BASE_ROUTE || '/api/v1/';
app.use(BASE_ROUTE + 'auth', authRoute);

// Optional: Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const port = process.env.PORT || 3000;

initializeSuperAdmin().then(() => {
  app.listen(port, () =>
    console.log(`AuthService running on port ${port}`)
  );
});