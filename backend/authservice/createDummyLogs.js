import fs from 'fs';
import path from 'path';
import zlib from 'zlib';

const LOG_DIR = path.resolve(process.cwd(), 'logs');
if (!fs.existsSync(LOG_DIR)) fs.mkdirSync(LOG_DIR, { recursive: true });

function pad(n) { return n < 10 ? '0' + n : n; }

const startDate = new Date('2025-06-01');
const endDate = new Date(); // today

for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
  const yyyy = d.getFullYear();
  const mm = pad(d.getMonth() + 1);
  const dd = pad(d.getDate());
  const fileName = `audit-${yyyy}-${mm}-${dd}.ndjson`;
  const filePath = path.join(LOG_DIR, fileName);
  fs.writeFileSync(filePath, `{"key":"test#${yyyy}-${mm}-${dd}","timestamp":"${yyyy}-${mm}-${dd}T00:00:00Z","userId":"test","username":"test","type":"LOGIN_ATTEMPT","status":"success","ipAddress":"::1","logDescription":"Login successful","attemptCount":0}\n`);
  // Zip the file
  const zipPath = filePath.replace('.ndjson', '.zip');
  const input = fs.createReadStream(filePath);
  const output = fs.createWriteStream(zipPath);
  const gzip = zlib.createGzip();
  input.pipe(gzip).pipe(output).on('finish', () => {
    fs.unlinkSync(filePath);
  });
}
console.log('Dummy zip files created from 2025-06-01 to today.');
