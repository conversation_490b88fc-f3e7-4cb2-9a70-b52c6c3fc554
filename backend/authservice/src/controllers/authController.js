import responseFormatter from '../middlewares/responseFormator.js';
import { addTokenToBlacklist, isTokenBlacklisted } from '../utils/tokenBlacklist.js';
import pool from '../config/db.js';
import recoveryQueries from '../utils/queries/recoveryQueries.js';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import AppConstants from '../config/appConstant.js';
import validateToken from '../middlewares/validateTokenHandler.js';
import queries from '../utils/queries/authQueries.js';
import StatusCodes from '../config/statusCodes.js';
import { logToNDJSON } from '../utils/ndjsonLogger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const {
    loginQuery,
    selectPasswordQuery,
    resetPasswordQuery,
    addRefreshTokenQuery,
    refreshTokenQuery,
    deleteRefreshTokenQuery,
} = queries;

const formatter = new responseFormatter();

//@desc   Login user and issue JWT
//@route  POST /api/v1/auth/login
//@access Public
const login = async (req, res) => {
    const { username, password } = req.body;
    const ipAddress = req.ip || null;
    let status = 'failure';
    let department = null, shift = null;
    let attemptCount = 0;
    // --- NDJSON login attempt lockout logic ---
    try {
        // Get today's log file path
        const logDir = path.resolve(__dirname, '../../logs');
        const today = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
        const logFile = path.join(logDir, `audit-${today}.ndjson`);
        // Count failed attempts before this attempt
        let failedAttempts = 0;
        if (fs.existsSync(logFile)) {
            const logLines = fs.readFileSync(logFile, 'utf8').split('\n').filter(Boolean);
            const now = Date.now();
            failedAttempts = logLines.filter(line => {
                try {
                    const entry = JSON.parse(line);
                    return entry.username === username &&
                        entry.type === 'LOGIN_ATTEMPT' &&
                        entry.status === 'failure' &&
                        entry.logDescription === 'Login failed: Invalid credentials' &&
                        (now - new Date(entry.timestamp).getTime()) < 15 * 60 * 1000;
                } catch { return false; }
            }).length;
        }
        if (failedAttempts >= 3) {
            return formatter.error('Account locked due to multiple failed login attempts. Try again after 15 minutes.', null, res, StatusCodes.FORBIDDEN);
        }
        // --- End of NDJSON logic ---

        // Fetch user by username
        const { rows } = await pool.query(
            loginQuery,
            [username]
        );
        const user = rows[0];
        if (!user) {
            logToNDJSON({
                key: `${username}#${new Date().toISOString()}`,
                timestamp: new Date().toISOString(),
                userId: null,
                username,
                type: 'LOGIN_ATTEMPT',
                status,
                ipAddress,
                department,
                shift,
                logDescription: 'Login failed: User does not exist',
                attemptCount: failedAttempts + 1
            });
            return formatter.error('User does not exist or account deleted', null, res, StatusCodes.UNAUTHORIZED);
        }
        if (user.is_deleted) {
            logToNDJSON({
                key: `${username}#${new Date().toISOString()}`,
                timestamp: new Date().toISOString(),
                userId: user.id,
                username,
                type: 'LOGIN_ATTEMPT',
                status,
                ipAddress,
                department,
                shift,
                logDescription: 'Login failed: Account deleted',
                attemptCount: failedAttempts + 1
            });
            return formatter.error('User does not exist or account deleted', null, res, StatusCodes.UNAUTHORIZED);
        }
        if (!user.is_active) {
            logToNDJSON({
                key: `${username}#${new Date().toISOString()}`,
                timestamp: new Date().toISOString(),
                userId: user.id,
                username,
                type: 'LOGIN_ATTEMPT',
                status,
                ipAddress,
                department,
                shift,
                logDescription: 'Login failed: Invalid credentials',
                attemptCount: failedAttempts + 1
            });
            return formatter.error('Invalid credentials', null, res, StatusCodes.UNAUTHORIZED);
        }

        // Compare passwords
        const match = await bcrypt.compare(password, user.password);
        if (!match) {
            logToNDJSON({
                key: `${username}#${new Date().toISOString()}`,
                timestamp: new Date().toISOString(),
                userId: user.id,
                username,
                type: 'LOGIN_ATTEMPT',
                status,
                ipAddress,
                department: user.department,
                shift: user.shift,
                logDescription: 'Login failed: Invalid credentials',
                attemptCount: failedAttempts + 1
            });
            return formatter.error('You have entered an invalid credential. Please enter the valid username or password.', null, res, StatusCodes.UNAUTHORIZED);
        }
        // On successful login, remove any old tokens for this user from blacklist
        if (isTokenBlacklisted(user.id)) {
            addTokenToBlacklist(user.id, null, false); // Remove userId from blacklist
        }
        // Generate JWT
        const token = jwt.sign(
            {
                id: user.id,
                username: user.username,
                firstname: user.firstname,
                lastname: user.lastname,
                email: user.email,
                role: user.role
            },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRY }
        );

        // Refresh Token with session id
        const refreshToken = jwt.sign(
            {
                id: user.id,
                username: user.username,
                firstname: user.firstname,
                lastname: user.lastname,
                email: user.email,
                role: user.role
            },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: process.env.JWT_REFRESH_EXPIRY + 'd' } // 7d
        );

        // Save refresh token in DB
        await pool.query(addRefreshTokenQuery, [user.id, refreshToken, process.env.JWT_REFRESH_EXPIRY]);

        // Build response user object
        const userPayload = {
            id: user.id,
            username: user.username,
            firstname: user.firstname,
            lastname: user.lastname,
            email: user.email,
            role: user.role,
            isPasswordReset: !user.must_reset_password
        };

        status = 'success';
        logToNDJSON({
            key: `${username}#${new Date().toISOString()}`,
            timestamp: new Date().toISOString(),
            userId: user.id,
            username,
            type: 'LOGIN_ATTEMPT',
            status,
            ipAddress,
            department: user.department,
            shift: user.shift,
            logDescription: 'Login successful',
            attemptCount: 0
        });

        formatter.success({ token, refreshToken, user: userPayload }, null, res, 'Login successful');
    } catch (error) {
        logToNDJSON({
            key: `${username}#${new Date().toISOString()}`,
            timestamp: new Date().toISOString(),
            userId: null,
            username,
            type: 'LOGIN_ATTEMPT',
            status,
            ipAddress,
            department,
            shift,
            logDescription: `Login error: ${error.message}`,
            attemptCount
        });
        formatter.error('Login failed', error, res);
    }
};

//@desc   Reset password for logged‑in user
//@route  POST /api/v1/auth/reset-password
//@access Private
const resetPassword = async (req, res) => {
    const { username, password } = req.body;
    const userId = req.user.id; // assuming authenticateJWT set this
    try {
        // 1) Fetch current hash
        const { rows } = await pool.query(
            selectPasswordQuery,
            [userId]
        );
        if (!rows.length) {
            return formatter.error('User not found', null, res, StatusCodes.NOT_FOUND);
        }

        // 2) Validate new password policy
        const pwdRegex = new RegExp(AppConstants.PASSWORD_POLICY_REGEX);
        if (!pwdRegex.test(password)) {
            return formatter.error('Password does not meet policy requirements', null, res, StatusCodes.BAD_REQUEST);
        }

        // 3) Hash & save
        const newHash = await bcrypt.hash(password, 10);
        await pool.query(
            resetPasswordQuery,
            [username, newHash, userId]
        );
        // Blacklist all tokens for this user after password reset
        addTokenToBlacklist(userId, req.headers['authorization']?.split(' ')[1]);
        formatter.success(null, null, res, 'Your Username and Password has been changed successfully.');
    } catch (error) {
        formatter.error('Password reset failed', error, res);
    }
};

// @desc   Validate Token
// @route  POST /api/v1/auth/validate-token
// @access Private
const validateTokenController = async (req, res) => {
    try {
        validateToken(req, res, (err) => {
            if (err) {
                return formatter.error('Invalid token', err, res, StatusCodes.UNAUTHORIZED);
            }
            // Check if token or userId is blacklisted using utility function
            const authHeader = req.headers['authorization'];
            const token = authHeader && authHeader.split(' ')[1];
            const userId = req.user?.id;
            // Block if either token or userId is blacklisted
            if (isTokenBlacklisted(token) || isTokenBlacklisted(userId)) {
                return formatter.error('Session expired, please login again.', null, res, StatusCodes.FORBIDDEN);
            }
            formatter.success({ valid: true, user: req.user }, null, res, 'Token is valid');
        });
    } catch (error) {
        formatter.error('Invalid token', error, res, StatusCodes.UNAUTHORIZED);
    }
};

// @desc   Refresh Token for logged-in users
// @route  POST /api/v1/auth/refresh-token
// @access Private
const refreshToken = async (req, res) => {
    const { token } = req.body;
    if (!token) return formatter.error('Refresh token is required', null, res, StatusCodes.BAD_REQUEST);

    try {
        // Verify and decode refresh token
        const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET);

        // Check token in DB
        const { rows } = await pool.query(
            refreshTokenQuery,
            [token, decoded.id]
        );

        if (rows.length === 0) {
            return formatter.error('Invalid or expired refresh token', null, res, StatusCodes.FORBIDDEN);
        }

        // Issue new access token with session id
        const newAccessToken = jwt.sign(
            {
                id: decoded.id,
                username: decoded.username,
                role: decoded.role,
                firstname: decoded.firstname,
                lastname: decoded.lastname,
                email: decoded.email,
                isPasswordReset: !decoded.must_reset_password
            },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRY }
        );

        formatter.success({ accessToken: newAccessToken }, null, res, 'Token refreshed');
    } catch (error) {
        formatter.error('Failed to refresh token', error, res);
    }
};

//@desc   Logout user
//@route  POST /api/v1/auth/refresh-token
//@access Private
const logout = async (req, res) => {
    const { token } = req.body;
    try {
        await pool.query(deleteRefreshTokenQuery, [token]);
        // Blacklist the access token for 1 day, key should be userId and value as token
        if (req.user && req.user.id) {
            addTokenToBlacklist(req.user.id, token);
        } else {
            addTokenToBlacklist(token, token); // fallback for legacy usage
        }
        formatter.success(null, null, res, 'User logged out successfully');
    } catch (error) {
        formatter.error('Logout failed', error, res);
    }
};

// Superadmin: Generate one-time recovery code for a user
const generateRecoveryCode = async (req, res) => {
    // Check if requester is superadmin
    if (req.user.role !== 'superadmin') {
        return formatter.error('Forbidden: Only superadmin can generate recovery code.', null, res, StatusCodes.FORBIDDEN);
    }
    const userId = req.user.id;
    if (!userId) {
        return formatter.error('Missing userId in token', null, res, StatusCodes.BAD_REQUEST);
    }
    try {
        // Block if active code exists
        const { rows: activeRows } = await pool.query(recoveryQueries.countActiveRecoveryCodes, [userId]);
        if (parseInt(activeRows[0].count) > 0) {
            return formatter.error('A recovery code is already active for this user. Please use the existing code.', null, res, StatusCodes.FORBIDDEN);
        }
        // Invalidate any previous unused codes
        await pool.query(recoveryQueries.invalidateUnusedCodes, [userId]);
        // Generate random code
        const code = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
        const codeHash = await bcrypt.hash(code, 10);
        await pool.query(recoveryQueries.insertRecoveryCode, [userId, codeHash]);
        // Return code in response (do not log or persist anywhere else)
        return formatter.success({ recoveryCode: code }, null, res, 'Recovery code generated. Please copy and save it securely.');
    } catch (error) {
        return formatter.error('Failed to generate recovery code', error, res);
    }
};

const verifyRecoveryCode = async (req, res) => {
    // Input: only code
    const { code } = req.body;
    if (!code) {
        return formatter.error('Missing recovery code', null, res, StatusCodes.BAD_REQUEST);
    }
    try {
        // Find unused recovery code
        const { rows } = await pool.query(recoveryQueries.selectUnusedRecoveryCodes);
        let found = null;
        for (const row of rows) {
            if (await bcrypt.compare(code, row.code_hash)) {
                found = row;
                break;
            }
        }
        if (!found) {
            return formatter.error('Invalid or already used recovery code.', null, res, StatusCodes.UNAUTHORIZED);
        }
        // Set must_reset_password=TRUE for user
        await pool.query(recoveryQueries.setMustResetPassword, [found.user_id]);
        // Mark code as used
        await pool.query(recoveryQueries.markCodeUsed, [found.id]);
        // Respond success
        return formatter.success(null, null, res, 'Recovery code verified. User must reset password.');
    } catch (error) {
        return formatter.error('Failed to verify recovery code', error, res);
    }
};

// @desc   Block a token for a user (add to blacklist)
// @route  POST /api/v1/auth/block-token
// @access Private
const blockToken = async (req, res) => {
    const { userId, token } = req.body;
    if (!userId && !token) {
        return formatter.error('userId or token are required', null, res, StatusCodes.BAD_REQUEST);
    }
    try {
        addTokenToBlacklist(userId, token);
        formatter.success(null, null, res, 'Token has been blacklisted');
    } catch (error) {
        formatter.error('Failed to blacklist token', error, res);
    }
};

export default {
    login,
    resetPassword,
    validateTokenController,
    refreshToken,
    logout,
    generateRecoveryCode,
    verifyRecoveryCode,
    blockToken,
};