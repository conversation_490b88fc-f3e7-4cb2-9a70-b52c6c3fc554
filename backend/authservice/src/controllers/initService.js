import User from '../models/userModel.js';
import { hashPassword } from '../utils/passwordUtil.js';
import UserRoles from '../config/appEnum.js';
import dotenv from 'dotenv';
dotenv.config();

const initializeSuperAdmin = async () => {
  try {
    const user = await User.findUserByRole(UserRoles.SUPERADMIN);
    if (!user) {
      const hash = await hashPassword(process.env.DEFAULT_SUPERADMIN_PASSWORD);
      await User.createUser({
        username: process.env.DEFAULT_SUPERADMIN_USERNAME,
        password: hash,
        role: UserRoles.SUPERADMIN,
        mustResetPassword: true
      });
      console.log('Super Admin initialized');
    }
  } catch (error) {
    throw new Error('Failed to initialize Super Admin');
  }
};

export default initializeSuperAdmin;
