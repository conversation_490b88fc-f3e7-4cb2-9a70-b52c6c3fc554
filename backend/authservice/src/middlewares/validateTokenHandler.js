import jwt from 'jsonwebtoken';
import ResponseFormatter from './responseFormator.js';
import StatusCodes from '../config/statusCodes.js';

const formatter = new ResponseFormatter();

const validateToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return formatter.error('Missing or malformed token', null, res, StatusCodes.UNAUTHORIZED);
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (err) {
        // Use 401 if token is expired or invalid
        const isAuthError =
            err.name === 'TokenExpiredError' || err.name === 'JsonWebTokenError';

        const status = isAuthError
            ? StatusCodes.UNAUTHORIZED
            : StatusCodes.FORBIDDEN;

        return formatter.error('Invalid or expired token', null, res, status);
    }
};

export default validateToken;