import express from 'express';
import authController from '../controllers/authController.js';
import { validateRequest } from '../validations/validate.js';
import {
    loginSchema,
    resetPasswordSchema,
    refreshTokenSchema,
} from '../models/authModel.js';
import validateToken from '../middlewares/validateTokenHandler.js'; // for protected routes

const authRoute = express.Router();

authRoute.get('/validate-token', authController.validateTokenController);

// Public login endpoint
// No validateToken middleware here
authRoute.post('/login', validateRequest({ bodySchema: loginSchema }), authController.login);
// Superadmin: Generate recovery code for a user
authRoute.get('/generate-recovery-code', validateToken, authController.generateRecoveryCode);

// Superadmin: Verify recovery code and reset password
authRoute.post('/verify-recovery-code', authController.verifyRecoveryCode);
// Protected reset-password endpoint
authRoute.post('/reset-password', validateToken, validateRequest({ bodySchema: resetPasswordSchema }), authController.resetPassword);
// Refresh token endpoint
authRoute.post('/refresh-token', validateRequest({ bodySchema: refreshTokenSchema }), authController.refreshToken);
authRoute.post('/logout', authController.logout);

// Block token endpoint
authRoute.post('/block-token', authController.blockToken);
export default authRoute;
