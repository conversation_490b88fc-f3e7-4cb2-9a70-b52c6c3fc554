import Joi from 'joi';
import AppConstants from '../config/appConstant.js';

// Schema for login request
export const loginSchema = Joi.object({
    username: Joi.string().alphanum().min(3).required().messages({
        'string.base': 'Username must be a string',
        'string.alphanum': 'Username must only contain letters and numbers',
        'string.min': 'Username must be at least 3 characters long',
        'any.required': 'Username is required',
    }),
    password: Joi.string().required()
});

// Schema for reset password request
export const resetPasswordSchema = Joi.object({
    username: Joi.string().alphanum().min(3).required().messages({
        'string.base': 'Username must be a string',
        'string.alphanum': 'Username must only contain letters and numbers',
        'string.min': 'Username must be at least 3 characters long',
        'any.required': 'Username is required',
    }),
    password: Joi.string()
        .pattern(new RegExp(AppConstants.PASSWORD_POLICY_REGEX))
        .required()
        .messages({
            'string.pattern.base': 'Password must be 8-16 chars, include upper, lower, number, and special char'
        })
});

// Schema for refresh token request
export const refreshTokenSchema = Joi.object({
    token: Joi.string().required().messages({
        'string.empty': 'Refresh token is required',
        'any.required': 'Refresh token is required'
    })
});

