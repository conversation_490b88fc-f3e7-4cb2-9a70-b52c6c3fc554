
import db from '../config/db.js';
import {
  findUserByRoleQuery,
  findUserByIdQuery,
  createUserQuery,
  updatePasswordQuery
} from '../utils/queries/userQueries.js';

const findUserByRole = async (role) => {
  const res = await db.query(findUserByRoleQuery, [role]);
  return res.rows[0];
};

const findUserById = async (id) => {
  const res = await db.query(findUserByIdQuery, [id]);
  return res.rows[0];
};

const createUser = async (user) => {
  const res = await db.query(
    createUserQuery,
    [user.username, user.password, user.role, user.mustResetPassword, false, true]
  );
  return res.rows[0];
};

const updatePassword = async (id, newHash) => {
  await db.query(updatePasswordQuery, [newHash, id]);
};

export default {
  findUserByRole,
  findUserById,
  createUser,
  updatePassword
};
