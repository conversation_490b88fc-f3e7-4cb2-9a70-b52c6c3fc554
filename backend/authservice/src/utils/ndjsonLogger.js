import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { combineLogZips } from './combineLogZips.js';
const LOG_DIR = path.resolve(process.cwd(), 'logs');

function ensureLogDir() {
  if (!fs.existsSync(LOG_DIR)) fs.mkdirSync(LOG_DIR);
}

function getLogFilePath(date = new Date()) {
  ensureLogDir();
  const d = typeof date === 'string' ? new Date(date) : date;
  const day = d.toISOString().slice(0, 10); // YYYY-MM-DD
  return path.join(LOG_DIR, `audit-${day}.ndjson`);
}

function zipFile(filePath) {
  ensureLogDir();
  const zipPath = filePath.replace('.ndjson', '.zip');
  if (fs.existsSync(zipPath)) return; // Already zipped
  const output = fs.createWriteStream(zipPath);
  const archive = archiver('zip', { zlib: { level: 9 } });
  archive.pipe(output);
  archive.file(filePath, { name: path.basename(filePath) });
  archive.finalize();
  output.on('close', () => {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath); // Remove original after zipping
    }
  });
}

function zipPreviousDayIfNeeded() {
  ensureLogDir();
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const yesterdayPath = getLogFilePath(yesterday);
  if (fs.existsSync(yesterdayPath)) {
    zipFile(yesterdayPath);
  }
}

function zipAllUnzippedNDJSONForMonth(monthStr) {
  ensureLogDir();
  const files = fs.readdirSync(LOG_DIR);
  files.forEach(file => {
    if (
      file.endsWith('.ndjson') &&
      file.startsWith(`audit-${monthStr}`)
    ) {
      const filePath = path.join(LOG_DIR, file);
      zipFile(filePath);
    }
  });
}

function archivePreviousMonthIfNeeded() {
  ensureLogDir();
  const today = new Date();
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const monthStr = lastMonth.toISOString().slice(0, 7); // YYYY-MM
  zipAllUnzippedNDJSONForMonth(monthStr); // Zip all unzipped NDJSON files for last month
  const archiveName = `logs-${monthStr}.zip`;
  const archivePath = path.join(LOG_DIR, archiveName);
  // Find all .zip files for last month
  const files = fs.readdirSync(LOG_DIR).filter(f => {
    return f.endsWith('.zip') && f.startsWith(`audit-${monthStr}`);
  });
  if (!files.length) return;
  const output = fs.createWriteStream(archivePath);
  const archive = archiver('zip', { zlib: { level: 9 } });
  archive.pipe(output);
  files.forEach(file => {
    archive.file(path.join(LOG_DIR, file), { name: file });
  });
  archive.finalize();
  output.on('close', () => {
    // Optionally delete daily zips after archiving
    files.forEach(file => fs.unlinkSync(path.join(LOG_DIR, file)));
  });
}

function zipAllPreviousNDJSON() {
  ensureLogDir();
  const todayStr = new Date().toISOString().slice(0, 10);
  const files = fs.readdirSync(LOG_DIR);
  files.forEach(file => {
    if (file.endsWith('.ndjson') && !file.includes(todayStr)) {
      const filePath = path.join(LOG_DIR, file);
      zipFile(filePath);
    }
  });
}

export function autoArchivePreviousMonths() {
  ensureLogDir();
  const files = fs.readdirSync(LOG_DIR);
  const now = new Date();
  // Find all months present in the logs, but only for months before the current month
  const monthSet = new Set();
  files.forEach(file => {
    const match = file.match(/^audit-(\d{4}-\d{2})-\d{2}\.zip$/);
    if (match) {
      const monthStr = match[1];
      const [year, month] = monthStr.split('-').map(Number);
      if (year < now.getFullYear() || (year === now.getFullYear() && month < now.getMonth() + 1)) {
        monthSet.add(monthStr);
      }
    }
  });
  monthSet.forEach(monthStr => {
    const archiveName = `logs-${monthStr}.zip`;
    const archivePath = path.join(LOG_DIR, archiveName);
    const dailyZips = files.filter(f => f.endsWith('.zip') && f.startsWith(`audit-${monthStr}`));
    if (dailyZips.length && !fs.existsSync(archivePath)) {
      combineLogZips(archiveName, file => file.endsWith('.zip') && file.startsWith(`audit-${monthStr}`));
      // Delete all daily zips for the month after archiving
      dailyZips.forEach(file => fs.unlinkSync(path.join(LOG_DIR, file)));
    }
  });
}

export function logToNDJSON(logObj) {
  ensureLogDir();
  zipAllPreviousNDJSON(); // Zip all previous NDJSON files before writing today's
  zipPreviousDayIfNeeded(); // Also zip yesterday's file if present
  autoArchivePreviousMonths(); // Archive only previous months and clean up
  archivePreviousMonthIfNeeded();
  const filePath = getLogFilePath();
  const line = JSON.stringify(logObj) + '\n';
  fs.appendFileSync(filePath, line, 'utf8');
}

export function archiveMonth(monthStr) {
  ensureLogDir();
  zipAllUnzippedNDJSONForMonth(monthStr); // Zip all unzipped NDJSON files for the month
  const archiveName = `logs-${monthStr}.zip`;
  const archivePath = path.join(LOG_DIR, archiveName);
  // Find all .zip files for the month
  const files = fs.readdirSync(LOG_DIR).filter(f => {
    return f.endsWith('.zip') && f.startsWith(`audit-${monthStr}`);
  });
  if (!files.length) return;
  const output = fs.createWriteStream(archivePath);
  const archive = archiver('zip', { zlib: { level: 9 } });
  archive.pipe(output);
  files.forEach(file => {
    archive.file(path.join(LOG_DIR, file), { name: file });
  });
  archive.finalize();
  output.on('close', () => {
    // Optionally delete daily zips after archiving
    files.forEach(file => fs.unlinkSync(path.join(LOG_DIR, file)));
  });
}