const countActiveRecoveryCodes = `SELECT COUNT(*) FROM recovery_codes WHERE user_id = $1 AND used = FALSE`;
const invalidateUnusedCodes = `UPDATE recovery_codes SET used = TRUE WHERE user_id = $1 AND used = FALSE`;
const insertRecoveryCode = `INSERT INTO recovery_codes (user_id, code_hash) VALUES ($1, $2)`;
const selectUnusedRecoveryCodes = `SELECT * FROM recovery_codes WHERE used = FALSE`;
const setMustResetPassword = `UPDATE users SET must_reset_password = TRUE WHERE id = $1`;
const markCodeUsed = `UPDATE recovery_codes SET used = TRUE WHERE id = $1`;

export default {
  countActiveRecoveryCodes,
  invalidateUnusedCodes,
  insertRecoveryCode,
  selectUnusedRecoveryCodes,
  setMustResetPassword,
  markCodeUsed,
};
