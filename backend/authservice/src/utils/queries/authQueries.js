const loginQuery = `SELECT id, username, password, firstname, lastname, email, role, departments, shifts, must_reset_password, is_active FROM users WHERE username = $1 AND is_deleted = FALSE`;
const selectPasswordQuery = `SELECT password FROM users WHERE id = $1`;
const resetPasswordQuery = `UPDATE users SET username = $1, password = $2, must_reset_password = FALSE WHERE id = $3`;
const addRefreshTokenQuery = `INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES ($1, $2, NOW() + ($3 || ' days')::interval)`;
const refreshTokenQuery = `SELECT * FROM refresh_tokens WHERE token = $1 AND user_id = $2 AND expires_at > NOW()`;
const deleteRefreshTokenQuery = `DELETE FROM refresh_tokens WHERE token = $1`;

export default {
    loginQuery,
    selectPasswordQuery,
    resetPasswordQuery,
    addRefreshTokenQuery,
    refreshTokenQuery,
    deleteRefreshTokenQuery,
};