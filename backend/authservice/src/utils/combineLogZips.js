import fs from 'fs';
import path from 'path';
import archiver from 'archiver';

const LOG_DIR = path.resolve(process.cwd(), 'logs');

/**
 * Combine all .zip log files in LOG_DIR for a given date range into one archive
 * @param {string} outputZipName - Name of the combined zip file (e.g., 'logs-2025-07.zip')
 * @param {function} filterFn - Optional filter function to select files (e.g., by month)
 */
export function combineLogZips(outputZipName, filterFn) {
  const archivePath = path.join(LOG_DIR, outputZipName);
  const output = fs.createWriteStream(archivePath);
  const archive = archiver('zip', { zlib: { level: 9 } });

  output.on('close', () => {
    console.log(`Combined zip created: ${archivePath} (${archive.pointer()} bytes)`);
  });

  archive.pipe(output);

  const files = fs.readdirSync(LOG_DIR);
  files.forEach(file => {
    if (file.endsWith('.zip') && (!filterFn || filterFn(file))) {
      archive.file(path.join(LOG_DIR, file), { name: file });
    }
  });

  archive.finalize();
}

// Example usage:
// combineLogZips('logs-2025-07.zip', file => file.startsWith('audit-2025-07'));
