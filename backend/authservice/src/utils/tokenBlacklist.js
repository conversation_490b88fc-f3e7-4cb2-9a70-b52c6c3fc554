import NodeCache from 'node-cache';

// Cache for blacklisted access tokens (TTL: 1 day)
const blacklistCache = new NodeCache({ stdTTL: 86400 });

// Add to blacklist by userId and token
export function addTokenToBlacklist(userId, token, value = true) {
    if (userId && token) {
        // Store token under userId and also as direct token for legacy lookup
        blacklistCache.set(userId, token);
        blacklistCache.set(token, value);
    } else if (token) {
        blacklistCache.set(token, value);
    } else if (userId) {
        blacklistCache.set(userId, value);
    }
}

// Check if token or userId is blacklisted
export function isTokenBlacklisted(tokenOrUserId) {
    // Log all keys and values in the cache for inspection
    // console.log('Checking blacklist for:', tokenOrUserId);
    const value = blacklistCache.get(tokenOrUserId);
    return !!value;
}

// Utility to get the whole blacklist cache (for debugging)
export function getBlacklistCache() {
    return blacklistCache.mget(blacklistCache.keys());
}
