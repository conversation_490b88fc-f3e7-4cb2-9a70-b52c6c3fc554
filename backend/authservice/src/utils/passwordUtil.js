import bcrypt from 'bcrypt';
import AppConstants from '../config/appConstant.js';

const hashPassword = (password) => bcrypt.hash(password, 10);
const comparePassword = (password, hash) => bcrypt.compare(password, hash);
const isValidPassword = (password) => {
  const regex = new RegExp(AppConstants.PASSWORD_POLICY_REGEX);
  return regex.test(password);
};

export {
  hashPassword,
  comparePassword,
  isValidPassword
};