MyShopCNC Deployment Instructions for Testers (Windows)

🧾 Step 1: Prerequisites (One-Time)
These must be done before running the .bat file

✅ Install Docker Desktop for Windows
Enable WSL2 or Hyper-V when prompted.
Restart the system if needed.

✅ Ensure Docker is running (check for the whale icon in the system tray)
📁 Step 2: Get the MyShopCNC Package
Download or extract the MyShopCNC folder you shared.

🖱️ Step 3: Run the Project
Right-click run-myshopcnc.bat → Choose “Run as Administrator” (recommended)

The script will:
Check if <PERSON><PERSON> is running
Load .tar images (if not already loaded)
Start all containers using docker-compose
Once complete, open your browser and visit:
http://localhost:8080/