## Install Prerequisites
Ensure the tester has these installed:
    ✅ Docker Desktop for Windows
        Enable WSL 2 backend if on Windows Home

## Setup Instructions
    Unzip the folder MyShopCNC.zip to any location (e.g., D:\MyShopCNC)
    Open Docker Desktop and make sure it’s running

## Build the Installer
    Open MyShopCNC_Setup.exe in the folder. Follow th instructuion on your desktop.
    I will create desktop application for myShopCNC project.

## Test It Yourself
    Run MyShopCNC_Installer.exe on a fresh machine
    Ensure:
        Files go to C:\Program Files (x86)\MyShopCNC
        Desktop shortcut is created
        It loads Docker images and opens the app      

## Run MyShopCNC launcher from app
    Check if Docker is running
    Load all Docker images (only once)
    Start all services using Docker Compose
    Detect your laptop IP and open the app in browser (e.g. http://{IP}:8080)
    ✅ Done!

Happy Testing!    


