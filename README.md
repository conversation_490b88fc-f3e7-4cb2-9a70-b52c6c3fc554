# MyShopCNC
A leading Machine Tool Solution provider, wishes to develop an On-Premise Machine Tool Health Monitoring Solution called “MyShopCNC Software Application”

## Getting Started
  - Clone the repo:
    git clone https://github.com/your-org/your-repo.git
    cd your-repo

## Environment Setup
- Each service has its own `.env` file.
  Copy from example:
  cp backend/userservice/.env.example backend/userservice/.env

- Build and start services with Docker:
  docker-compose up --build

## Folder Structure for a MyShopCNC project
/MyShopCNC
├── backend/
│ ├── dashboardservice/ # Node.js backend service (Dashboard)
│ │ ├── Dockerfile
│ │ ├── package.json
│ │ ├── server.js
│ │ ├── src/
├── config/ # App config, env loader, constants
│ ├── index.js
│ └── db.js # DB connection
│
├── controllers/ # Route handlers
│ └── dashboardController.js
│
├── services/ # Business logic
│ └── dashboardService.js
│
├── models/ # Mongoose/Sequelize schemas or DTOs
│ └── dashboardModel.js
│
├── routes/ # API route definitions
│ └── dashboardRoutes.js
│
├── middlewares/ # Custom middlewares (auth, validation)
│ └── authMiddleware.js
│
├── utils/ # Helper functions
│ └── logger.js
│
├── validations/ # Joi, Zod, or custom validators
│ └── dashboardValidation.js
│
├── jobs/ # (Optional) Background jobs or cron tasks
│ └── syncJob.js
│
├── tests/ # Unit and integration tests
│ └── user.test.js
│ ├── userservice/ # Node.js backend service (User)
│ │ ├── Dockerfile
│ │ ├── package.json
│ │ ├── server.js
│ │ ├── src/
| ├── .env
├── timescaledb/ # TimescaleDB Database
│ ├── docker-compose.override.yml
│ ├── migrations/
│ ├── seed-data/
├── frontend/ # Angular Frontend
│ ├── Dockerfile
│ ├── nginx.conf
│ ├── package.json
│ ├── src/
│ ├── dist/<your-angular-app-name>/ # Build output
| ├── .env
├── nginx/ # Nginx Reverse Proxy
│ ├── nginx.conf
│ ├── Dockerfile
├── docker-compose.yml # Root Docker Compose
├── .env # Environment variables
├── README.md # Documentation

# Coding guidlines
- Use Prettier and ESLint to format and lint your code.
- Run linting before committing:
  - npm run lint
  - npm run format

# Local development
- Uncomment code from below function
  - validateAuthToken  path backend>userservice>src>middleware>validateAuthToken.js
- Uncomment baseUrl and authUrl for local testing
  - app.constant.ts  path frontend>src>app>app.constant.ts
- Add host in .env file
  - host=localhost
