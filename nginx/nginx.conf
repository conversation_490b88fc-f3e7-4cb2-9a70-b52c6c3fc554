worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout 65;

    server {
        listen 80;

        # Serve Angular frontend
        location / {
            alias /var/www/html/;
            try_files $uri $uri/ /index.html;
            index index.html;  # Ensure index.html is used as the directory index
        }

        # Serve favicon.ico explicitly
        location = /favicon.ico {
            root /var/www/html;
            try_files /favicon.ico =404;  # Ensure the favicon is served correctly
        }

        # Serve images from /var/www/html/images/
        location /images/ {
            alias /var/www/html/images/;
            autoindex on;  # Optional: to list the contents of the directory if needed
        }

        # Serve icons from /var/www/html/icons/
        location /icons/ {
            alias /var/www/html/icons/;
            autoindex on;  # Optional: to list the contents of the directory if needed
        }

        # Serve icons from /var/www/html/SideBarIcons/
        location /SideBarIcons/ {
            alias /var/www/html/SideBarIcons/;
            autoindex on;  # Optional: to list the contents of the directory if needed
        }

        # === Proxy API calls with proper CORS ===
        location ~* ^/api/ {
            if ($request_method = OPTIONS) {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
                add_header 'Access-Control-Allow-Headers' 'Origin, Content-Type, Accept, Authorization' always;
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }

            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, Content-Type, Accept, Authorization' always;
        }

        # Reusable proxy headers (inline)
        set $proxy_headers "
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        ";

        # Auth service
        location = /api/auth {
            return 301 /api/auth/;
        }
        location ^~ /api/auth/ {
            proxy_pass http://authservice:3000/api/v1/auth/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User service - department
        location = /api/department {
            return 301 /api/department/;
        }
        location ^~ /api/department/ {
            proxy_pass http://userservice:3001/api/v1/department/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User service - shift
        location = /api/shift {
            return 301 /api/shift/;
        }
        location ^~ /api/shift/ {
            proxy_pass http://userservice:3001/api/v1/shift/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User service - user
        location = /api/user {
            return 301 /api/user/;
        }
        location ^~ /api/user/ {
            proxy_pass http://userservice:3001/api/v1/user/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User service - setting
        location = /api/setting {
            return 301 /api/setting/;
        }
        location ^~ /api/setting/ {
            proxy_pass http://userservice:3001/api/v1/setting/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Serve uploaded images
        location /appimages/ {
            proxy_pass http://userservice:3001/appimages/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Fallback for undefined /api/ routes
        location ^~ /api/ {
            return 404;
        }

        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}
